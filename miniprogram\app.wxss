/**app.wxss**/
/* 导入本地字体 */
@font-face {
  font-family: 'TDesign-Icon';
  src: url('https://tdesign.gtimg.com/icon/0.3.2/fonts/t.woff') format('woff'),
       url('./assets/fonts/t.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

page {
  background: #f6f6f6;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
} 

button {
  background: initial;
}

button:focus{
  outline: 0;
}

button::after{
  border: none;
}

/* 全局字体设置 */
view, text, button, input, textarea {
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* TDesign图标字体设置 */
.t-icon {
  font-family: 'TDesign-Icon' !important;
}

/* 引入TDesign组件样式覆盖 */
@import './styles/tdesign-override.wxss';

