<!-- miniprogram/pages/album-management/album-management.wxml -->
<view class="container">
  <!-- 如果没有照片则显示提示 -->
  <view class="empty-container" wx:if="{{ photoGroups.length === 0 && isAdmin }}">
    <t-icon name="image-error" size="80rpx" color="#dcdcdc"/>
    <view class="empty-text">相册还是空的，快上传第一张照片吧！</view>
  </view>

  <!-- 照片网格 -->
  <scroll-view class="photo-scroll-view" scroll-y="true">
    <block wx:for="{{photoGroups}}" wx:for-item="group" wx:for-index="groupIndex" wx:key="date">
      <view class="date-header">{{group.date}}</view>
      <view class="photo-grid">
        <view class="photo-item-wrapper" wx:for="{{group.photos}}" wx:for-item="photo" wx:for-index="photoIndex" wx:key="fileID">
          <view class="photo-item"
                data-fileid="{{photo.fileID}}"
                data-groupindex="{{groupIndex}}"
                data-photoindex="{{photoIndex}}"
                bind:tap="onImageTap"
                bind:longpress="onImageLongPress">
            
            <image class="photo-image" src="{{photo.tempFileURL}}" mode="aspectFill" lazy-load="true"/>

            <!-- 排序模式下的数字角标 -->
            <view class="order-badge" wx:if="{{isOrderingMode && photo.homepageOrder > 0}}">
              {{photo.homepageOrder}}
            </view>

            <!-- 选择模式下的遮罩 -->
            <view class="selection-overlay" wx:if="{{photo.selected}}">
              <view class="checkmark"></view>
            </view>

            <!-- 首页图片角标 (非排序模式下显示) -->
            <view class="homepage-badge" wx:if="{{!isOrderingMode && photo.homepageOrder > 0}}">
              <t-icon class="homepage-star-icon" name="star-filled" size="24rpx" color="#fff"/>
            </view>

          </view>
        </view>
      </view>
    </block>
  </scroll-view>

  <!-- 浮动上传按钮 -->
  <view class="fab-container" wx:if="{{isAdmin && !selectionMode && !isOrderingMode}}">
    <button class="fab" bind:tap="onUploadTap">
      <t-icon name="add" size="60rpx" color="#fff"/>
    </button>
  </view>

  <!-- 底部操作栏 -->
  <view class="action-bar-container" wx:if="{{selectionMode || isOrderingMode}}">
    <!-- 选择模式操作栏 -->
    <view class="action-bar" wx:if="{{selectionMode}}">
      <button class="action-button primary" bind:tap="handleDeleteSelected">删除</button>
      <button class="action-button" bind:tap="enterOrderingMode">管理首页图片</button>
      <button class="action-button secondary" bind:tap="cancelSelection">取消</button>
    </view>

    <!-- 排序模式操作栏 -->
    <view class="action-bar" wx:if="{{isOrderingMode}}">
      <button class="action-button primary" bind:tap="saveHomepageOrder">保存顺序</button>
      <button class="action-button secondary" bind:tap="cancelOrdering">取消</button>
    </view>
  </view>
</view>