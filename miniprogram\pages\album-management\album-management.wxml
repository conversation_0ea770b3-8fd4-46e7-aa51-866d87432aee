<t-toast id="t-toast" />
<!-- 相册管理页面 -->
<view class="album-management-container">
  <!-- 操作按钮区域 -->
  <view class="album-header">
    <t-button theme="default" size="medium" class="action-btn" wx:if="{{!chooseBannerMode}}" bindtap="onChooseBannerMode">
      <t-icon name="edit" slot="prefix" />选择首页图片
    </t-button>
    <t-button theme="primary" size="medium" class="action-btn" wx:if="{{chooseBannerMode}}" bindtap="onCancelChooseBanner">
      <t-icon name="check" slot="prefix" />完成选择
    </t-button>
    <t-button theme="danger" size="medium" class="action-btn" style="margin-left: 24rpx;" wx:if="{{!deleteMode}}" bindtap="onDeleteMode">
      <t-icon name="delete" slot="prefix" />删除模式
    </t-button>
    <t-button theme="primary" size="medium" class="action-btn" style="margin-left: 24rpx;" wx:if="{{deleteMode}}" bindtap="onCancelDeleteMode">
      <t-icon name="check" slot="prefix" />完成删除
    </t-button>
  </view>

  <!-- 图片网格区域 -->
  <view class="album-grid">
    <!-- 上传图片按钮 - 放在第一个位置 -->
    <view class="album-item upload-item" bindtap="onUploadImage">
      <view class="upload-btn-container">
        <t-icon name="add" size="32" color="#999" />
        <text class="upload-text">上传图片</text>
      </view>
      <!-- 上传状态提示 -->
      <view class="upload-status" wx:if="{{isUploading}}">
        <t-loading size="16" />
        <text class="uploading-text">上传中...</text>
      </view>
    </view>

    <!-- 已上传的图片 -->
    <view class="album-item" wx:for="{{albumImages}}" wx:key="_id">
      <!-- 首页编号圆圈 -->
      <view wx:if="{{item.bannerOrder}}" class="banner-order-badge">{{item.bannerOrder}}</view>

      <!-- 删除模式下的复选框 -->
      <checkbox wx:if="{{deleteMode}}" class="delete-checkbox" checked="{{item.selected}}" data-index="{{index}}" bindchange="onSelectImage" />

      <!-- 图片 -->
      <image
        src="{{item.tempFileURL}}"
        mode="aspectFill"
        class="album-img"
        data-index="{{index}}"
        bindtap="{{deleteMode ? 'onSelectImage' : (chooseBannerMode ? 'onToggleBanner' : 'onPreviewImage')}}"
      />
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-item">
      <t-loading size="24" />
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 没有更多提示 - 占用一张图片的位置 -->
    <view wx:if="{{!hasMore && albumImages.length > 0}}" class="album-item end-item">
      <view class="end-content">
        <t-icon name="check-circle" size="24" color="#999" />
        <text class="end-text">没有更多了</text>
      </view>
    </view>
  </view>

  <!-- 提示信息 -->
  <view class="album-warning">
    <text><t-icon name="info-circle" size="16" color="#fa7d3c" /> 相册中的</text><text class="highlight">全部</text><text>图片对</text><text class="highlight">任何</text><text>用户都</text><text class="highlight">可见</text><text>，用户可在首页点击静态图片，翻阅整个相册</text>
  </view>
</view>