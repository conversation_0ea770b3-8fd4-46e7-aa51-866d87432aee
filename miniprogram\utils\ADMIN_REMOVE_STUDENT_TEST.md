# 管理员移除学员预约功能测试指南

## 测试前准备

### 1. 测试环境
- 确保有管理员账号和普通学员账号
- 准备一个有学员预约的课程
- 确保云函数已部署最新版本

### 2. 测试数据
- **管理员账号**：具有"管理员"角色的用户
- **学员账号**：具有"学员"角色的用户
- **测试课程**：至少有1个学员预约的课程

## 测试用例

### 测试用例1：权限验证
**目的**：验证只有管理员能看到和使用移除功能

**步骤**：
1. 使用普通学员账号登录
2. 进入course-management页面
3. 展开任意有预约的课程的学员列表

**预期结果**：
- 学员列表中不显示"移除"按钮
- 只显示学员姓名和图标

**步骤**：
4. 切换到管理员账号登录
5. 进入course-management页面
6. 展开同一课程的学员列表

**预期结果**：
- 每个学员名字后面都显示"移除"按钮
- 按钮为红色outline样式，靠右对齐

### 测试用例2：移除功能基本流程
**目的**：验证完整的移除流程

**步骤**：
1. 使用管理员账号登录
2. 进入course-management页面
3. 找到有预约学员的课程
4. 点击"已预约学员"展开学员列表
5. 点击某个学员后面的"移除"按钮

**预期结果**：
- 弹出确认对话框
- 对话框标题为"移除学员预约"
- 内容包含学员姓名和操作说明
- 确认按钮显示"确认移除(5)"
- 开始5秒倒计时

**步骤**：
6. 等待倒计时结束
7. 点击"确认移除"按钮

**预期结果**：
- 显示"移除中..."加载提示
- 操作成功后显示"已为学员XXX取消预约"
- 对话框自动关闭
- 页面刷新，该学员从列表中消失
- 预约人数减少1

### 测试用例3：倒计时机制
**目的**：验证5秒倒计时防误操作机制

**步骤**：
1. 点击"移除"按钮打开确认对话框
2. 在倒计时期间（1-4秒）点击"确认移除"按钮

**预期结果**：
- 显示"请等待倒计时结束"提示
- 操作不会执行
- 对话框保持打开状态

**步骤**：
3. 等待倒计时结束（0秒）
4. 再次点击"确认移除"按钮

**预期结果**：
- 操作正常执行
- 学员预约被成功取消

### 测试用例4：取消操作
**目的**：验证取消操作的正确性

**步骤**：
1. 点击"移除"按钮打开确认对话框
2. 点击"取消"按钮

**预期结果**：
- 对话框立即关闭
- 倒计时定时器被清除
- 学员预约状态不变
- 页面数据不变

### 测试用例5：考勤卡次数退还
**目的**：验证考勤卡次数是否正确退还

**前置条件**：
- 学员有有效的考勤卡
- 学员的预约是通过考勤卡次数扣除的

**步骤**：
1. 记录学员当前的考勤卡剩余次数
2. 执行移除学员预约操作
3. 检查学员的考勤卡剩余次数

**预期结果**：
- 考勤卡剩余次数增加1
- 预约状态变为"已取消"
- 取消原因记录为"管理员取消"

### 测试用例6：时间限制测试
**目的**：验证不受时间限制的特性

**测试场景A：未开始的活动**
1. 选择开始时间在未来的课程
2. 执行移除操作

**预期结果**：操作成功

**测试场景B：进行中的活动**
1. 选择当前时间在开始和结束时间之间的课程
2. 执行移除操作

**预期结果**：操作成功

**测试场景C：已结束的活动**
1. 选择结束时间在过去的课程
2. 执行移除操作

**预期结果**：操作成功

### 测试用例7：错误处理
**目的**：验证各种错误情况的处理

**测试场景A：网络错误**
1. 断开网络连接
2. 执行移除操作

**预期结果**：
- 显示"移除失败: 网络错误"提示
- 对话框保持打开状态

**测试场景B：重复操作**
1. 快速连续点击同一个学员的移除按钮

**预期结果**：
- 只打开一个确认对话框
- 不会出现多个对话框重叠

**测试场景C：数据不一致**
1. 在移除过程中，其他管理员同时取消了该预约

**预期结果**：
- 显示相应的错误提示
- 页面自动刷新显示最新状态

### 测试用例8：UI样式验证
**目的**：验证UI样式的正确性

**检查项目**：
1. 移除按钮是否靠右对齐
2. 多个移除按钮是否上下对齐
3. 按钮样式是否为红色outline
4. 按钮大小是否为extra-small
5. 学员名字和按钮之间是否有适当间距

### 测试用例9：数据库记录验证
**目的**：验证数据库中的操作记录

**步骤**：
1. 执行移除操作
2. 查看数据库中的预约记录

**预期结果**：
- 预约记录的status字段变为"cancelled"
- cancelTime字段记录了取消时间
- cancelReason字段为"管理员取消"
- cancelBy字段为"admin"
- adminOpenid字段记录了操作管理员的openid

### 测试用例10：页面刷新验证
**目的**：验证操作后页面数据的更新

**步骤**：
1. 记录操作前的课程预约人数
2. 执行移除操作
3. 观察页面数据变化

**预期结果**：
- 预约人数立即减少1
- 学员从列表中消失
- 如果是最后一个学员，显示"暂无预约学员"
- 其他课程的数据不受影响

## 性能测试

### 测试用例11：大量学员的课程
**目的**：验证在学员较多时的性能

**步骤**：
1. 选择有20+学员预约的课程
2. 展开学员列表
3. 执行移除操作

**预期结果**：
- 页面响应流畅，无明显卡顿
- 移除操作在合理时间内完成（<3秒）

## 兼容性测试

### 测试用例12：不同设备测试
**目的**：验证在不同设备上的兼容性

**测试设备**：
- iPhone（iOS）
- Android手机
- 微信开发者工具

**检查项目**：
- 按钮显示是否正常
- 对话框是否正确显示
- 倒计时是否正常工作

## 回归测试

### 测试用例13：原有功能验证
**目的**：确保新功能不影响原有功能

**检查项目**：
1. 课程列表加载是否正常
2. 课程编辑功能是否正常
3. 学员预约功能是否正常
4. 学员自主取消预约是否正常
5. 课程状态切换是否正常

## 测试报告模板

```
测试日期：____
测试人员：____
测试环境：____

测试结果：
□ 通过  □ 失败

失败用例：
- 用例编号：____
- 失败原因：____
- 重现步骤：____

建议：
____
```
