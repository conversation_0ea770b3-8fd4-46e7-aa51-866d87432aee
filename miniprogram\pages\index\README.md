# 首页门店公告功能修改说明

## 功能概述

为首页的门店公告区域添加了点击弹出层功能，限制显示2行文字，点击后使用TDesign的Popup组件展示完整的门店公告内容。

## 修改内容

### 1. WXML文件修改 (`index.wxml`)

**门店公告区域：**
- 添加了 `bind:tap="showAnnouncementPopup"` 点击事件
- 为公告文本添加了 `announcement-text-limited` 类名
- 添加了更多按钮图标 `<t-icon name="chevron-right" size="12" />`

**弹出层组件：**
- 添加了TDesign的 `t-popup` 组件
- 使用 `placement="center"` 居中弹出
- 包含标题栏和内容区域
- 使用TDesign的 `close-circle` 图标作为关闭按钮
- 关闭按钮位于弹出层外部下方，白色圆形设计
- 支持点击遮罩层关闭

### 2. JS文件修改 (`index.js`)

**数据添加：**
- 在 `data` 中添加了 `showAnnouncement: false` 控制弹出层显示状态

**方法添加：**
- `showAnnouncementPopup()` - 显示门店公告弹出层
- `closeAnnouncementPopup()` - 关闭门店公告弹出层

### 3. WXSS文件修改 (`index.wxss`)

**公告容器样式：**
- 添加了 `cursor: pointer` 手型光标
- 添加了点击时的背景色变化效果
- 添加了过渡动画

**文本限制样式：**
- 使用 `-webkit-line-clamp: 2` 限制显示2行
- 添加了省略号效果
- 为更多按钮预留了空间

**更多按钮样式：**
- 绝对定位在右侧
- 添加了半透明背景和圆角
- 居中对齐

**弹出层样式：**
- 自定义弹出层内容样式
- 响应式宽度设计（80vw，最大320px）
- 最大高度限制（70vh）
- 添加了阴影效果
- 支持内容滚动
- 关闭按钮使用TDesign的 `close-circle` 图标，64rpx大小，白色
- 关闭按钮绝对定位在弹出层外部下方，居中显示

### 4. JSON文件修改 (`index.json`)

**组件引用：**
- 添加了 `t-popup` 组件的引用

## 功能特点

### 1. 用户体验
- **视觉提示**：添加了手型光标和点击反馈
- **渐进式显示**：先显示2行预览，点击查看完整内容
- **直观操作**：右侧箭头图标提示可点击

### 2. 响应式设计
- **自适应宽度**：弹出层宽度为80vw，最大320px
- **高度限制**：最大高度为70vh，超出时可滚动
- **安全区域**：适配不同屏幕尺寸

### 3. 交互设计
- **多种关闭方式**：点击外部圆圈关闭按钮、点击遮罩层、ESC键
- **平滑动画**：弹出和关闭都有过渡效果，关闭按钮有点击缩放效果
- **状态管理**：正确管理弹出层的显示状态

### 4. 样式设计
- **TDesign规范**：遵循TDesign设计规范
- **一致性**：与页面整体风格保持一致
- **可读性**：合理的字体大小和行高

## 技术实现

### 1. 文本截断
```css
.announcement-text-limited {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}
```

### 2. 弹出层控制
```javascript
// 显示弹出层
showAnnouncementPopup() {
  this.setData({
    showAnnouncement: true
  });
}

// 关闭弹出层
closeAnnouncementPopup() {
  this.setData({
    showAnnouncement: false
  });
}
```

### 3. 组件配置
```json
{
  "usingComponents": {
    "t-popup": "tdesign-miniprogram/popup/popup"
  }
}
```

## 使用说明

1. **查看公告**：点击门店公告区域即可查看完整内容
2. **关闭弹出层**：
   - 点击弹出层下方的白色圆圈关闭按钮
   - 点击弹出层外的遮罩区域
   - 按ESC键（如果支持）

## 兼容性

- 支持微信小程序基础库 2.0.0 及以上版本
- 使用TDesign组件库，确保版本兼容性
- 响应式设计适配各种屏幕尺寸

## 注意事项

1. **文本长度**：建议公告文本不要过长，避免弹出层内容过多
2. **性能优化**：弹出层组件按需加载，不影响页面性能
3. **无障碍访问**：提供了多种关闭方式，提升可访问性 