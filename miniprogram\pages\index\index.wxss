/* index.wxss */
/*
 * 这是微信小程序的样式文件，相当于传统Web开发中的CSS文件
 *
 * WXSS (WeiXin Style Sheets) 语法说明：
 * WXSS基本与CSS相同，但有一些小程序特有的扩展和限制
 *
 * 主要区别对比：
 *
 * 1. 尺寸单位差异：
 *    - CSS: px, em, rem, %, vw, vh等
 *    - WXSS: 除了CSS单位外，还支持rpx（responsive pixel）
 *    - rpx: 响应式像素，会根据屏幕宽度自动调整
 *    - 换算：iPhone6屏幕宽度375px = 750rpx，即1px = 2rpx
 *
 * 2. 样式导入：
 *    - CSS: @import url("style.css");
 *    - WXSS: @import "style.wxss";
 *
 * 3. 选择器支持：
 *    - CSS: 支持所有选择器（伪类、属性选择器、兄弟选择器等）
 *    - WXSS: 只支持基础选择器（类、ID、元素、后代、子选择器等）
 *
 * 4. 全局样式：
 *    - CSS: 通过link标签引入或style标签定义
 *    - WXSS: app.wxss为全局样式，页面样式会覆盖全局样式
 *
 * 5. 内联样式：
 *    - HTML: <div style="color: red;">
 *    - WXML: <view style="color: red;">（用法相同）
 */

/**
 * .container: 根容器样式类
 * 作用类似于Web开发中的body或main容器
 *
 * 盒模型知识回顾：
 * - content: 内容区域
 * - padding: 内边距（内容与边框之间的距离）
 * - border: 边框
 * - margin: 外边距（元素与其他元素之间的距离）
 *
 * box-sizing属性：
 * - content-box: 默认值，width/height只包含content
 * - border-box: width/height包含content + padding + border
 */
.container {
  /*
   * padding: 内边距设置
   * 语法：padding: 上 右 下 左; 或 padding: 上下 左右; 或 padding: 全部;
   * 16px表示所有四个方向都是16像素的内边距
   *
   * 与您熟悉的C#对比：类似于WPF的Margin或Padding属性
   * 与Java对比：类似于Swing的Insets或Android的padding
   */
  padding: 16px;

  /*
   * padding-bottom: 底部内边距特殊设置
   * 为底部tabBar预留空间，避免内容被遮挡
   *
   * calc()函数：CSS计算函数，可以进行数学运算
   * 语法：calc(表达式)，支持 +、-、*、/ 运算
   * 注意：运算符前后必须有空格
   *
   * env()函数：CSS环境变量函数，获取系统环境信息
   * safe-area-inset-bottom：底部安全区域高度
   * 主要用于适配iPhone X等全面屏手机的底部安全区域
   */
  padding-bottom: calc(16px + 120rpx + env(safe-area-inset-bottom));

  /*
   * background-color: 背景颜色
   * 颜色值格式：
   * - 十六进制：#f5f5f5（最常用）
   * - RGB：rgb(245, 245, 245)
   * - RGBA：rgba(245, 245, 245, 1)（带透明度）
   * - 颜色名：lightgray
   *
   * #f5f5f5是浅灰色，常用于页面背景
   */
  background-color: #f5f5f5;

  /*
   * min-height: 最小高度
   * 100vh表示视口高度的100%（viewport height）
   * 确保容器至少占满整个屏幕高度，即使内容不足
   *
   * 视口单位：
   * - vw: 视口宽度的1%（1vw = 视口宽度/100）
   * - vh: 视口高度的1%（1vh = 视口高度/100）
   * - vmin: vw和vh中较小的值
   * - vmax: vw和vh中较大的值
   */
  min-height: 100vh;

  /*
   * font-family: 字体族设置
   * 定义字体优先级列表，浏览器会按顺序查找可用字体
   *
   * 字体说明：
   * - "PingFang SC": 苹果系统中文字体（简体中文）
   * - "Helvetica Neue": 苹果系统英文字体
   * - "Arial": Windows系统常用字体
   * - sans-serif: 无衬线字体族（兜底字体）
   *
   * 字体分类：
   * - serif: 衬线字体（如Times New Roman），适合正文阅读
   * - sans-serif: 无衬线字体（如Arial），适合界面显示
   * - monospace: 等宽字体（如Courier），适合代码显示
   */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;

  /*
   * box-sizing: 盒模型计算方式
   *
   * 两种模式对比：
   * - content-box（默认）: width = content宽度
   *   实际占用宽度 = width + padding + border
   * - border-box: width = content + padding + border
   *   实际占用宽度 = width
   *
   * border-box更符合直觉，设置width:100px，元素就占用100px宽度
   * 现代CSS开发中通常全局设置为border-box
   */
  box-sizing: border-box;
}

/**
 * 视差滚动效果样式区域
 *
 * 视差滚动(Parallax Scrolling)原理：
 * 不同层级的元素以不同速度滚动，产生立体感和深度感
 * 常见于现代网站的首屏展示区域
 *
 * 实现方式：
 * 1. 背景图片固定或缓慢移动
 * 2. 前景内容正常滚动
 * 3. 通过transform: scale()实现缩放效果
 * 4. 使用z-index控制层级关系
 */

/**
 * .parallax-header: 视差滚动容器
 * 作为视差效果的外层容器，控制整体布局和尺寸
 */
.parallax-header {
  /*
   * position: relative - 相对定位
   * 作为绝对定位子元素的参考点
   * 子元素的absolute定位会相对于这个容器
   *
   * CSS定位类型对比：
   * - static: 默认定位，按文档流排列
   * - relative: 相对定位，相对于自身原始位置偏移
   * - absolute: 绝对定位，相对于最近的非static父元素
   * - fixed: 固定定位，相对于视口固定
   * - sticky: 粘性定位，滚动时在relative和fixed间切换
   */
  position: relative;

  /*
   * overflow: hidden - 隐藏溢出内容
   * 当子元素超出容器边界时，超出部分会被裁剪
   *
   * overflow属性值：
   * - visible: 默认值，溢出内容可见
   * - hidden: 隐藏溢出内容
   * - scroll: 始终显示滚动条
   * - auto: 需要时显示滚动条
   *
   * 这里用于裁剪缩放后的背景图片，保持容器边界整洁
   */
  overflow: hidden;

  /*
   * width: 100% - 宽度占满父容器
   * 百分比单位相对于父元素的对应属性
   */
  width: 100%;

  /*
   * height: 18vh - 高度为视口高度的18%
   * vh单位：viewport height，视口高度的百分比
   * 18vh约等于屏幕高度的1/5，适合作为头部展示区域
   *
   * 高度单位选择建议：
   * - px: 固定高度，不同屏幕显示效果可能差异较大
   * - vh: 相对视口高度，适配不同屏幕尺寸
   * - %: 相对父元素高度，需要父元素有明确高度
   */
  height: 18vh;

  /*
   * margin-bottom: 16px - 下外边距
   * 与下方元素保持16像素的间距
   * margin用于控制元素间的距离
   */
  margin-bottom: 16px;

  /*
   * border-radius: 8px - 圆角边框
   * 设置容器四个角的圆角半径为8像素
   *
   * border-radius语法：
   * - border-radius: 8px; (四个角相同)
   * - border-radius: 8px 4px; (上下8px，左右4px)
   * - border-radius: 8px 4px 2px 1px; (顺时针：上右下左)
   *
   * 现代UI设计中，适度的圆角可以让界面更加柔和友好
   */
  border-radius: 8px;

  /*
   * box-shadow: 阴影效果
   * 语法：box-shadow: x偏移 y偏移 模糊半径 扩展半径 颜色;
   *
   * 参数解析：
   * - 0: x轴偏移，0表示不向左右偏移
   * - 2px: y轴偏移，向下偏移2像素
   * - 8px: 模糊半径，数值越大阴影越模糊
   * - rgba(0, 0, 0, 0.08): 阴影颜色，黑色8%透明度
   *
   * rgba颜色格式：
   * - r: red红色值 (0-255)
   * - g: green绿色值 (0-255)
   * - b: blue蓝色值 (0-255)
   * - a: alpha透明度 (0-1)
   *
   * 这里创建了一个轻微的下方阴影，增加层次感
   */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

  /* 字体设置，继承全局字体配置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/**
 * .parallax-bg: 视差背景图片样式
 * 实现视差滚动效果的核心元素
 */
.parallax-bg {
  /*
   * position: absolute - 绝对定位
   * 相对于最近的relative父元素(.parallax-header)定位
   * 脱离文档流，不占用空间
   */
  position: absolute;

  /* 宽高100%，填满父容器 */
  width: 100%;
  height: 100%;

  /*
   * top: 0; left: 0; - 定位到父容器的左上角
   * 与父容器完全重合
   */
  top: 0;
  left: 0;

  /*
   * z-index: 0 - 层级设置
   * 数值越大，层级越高，越在上方显示
   * 0是最低层级，作为背景图片
   *
   * 层级规划：
   * - z-index: 0 - 背景图片
   * - z-index: 1 - 遮罩层
   * - z-index: 2 - 文字内容
   */
  z-index: 0;

  /*
   * object-fit: contain - 图片适应方式
   * 控制图片如何适应容器尺寸
   *
   * object-fit属性值：
   * - contain: 保持比例，完整显示图片，可能有空白
   * - cover: 保持比例，填满容器，可能裁剪图片
   * - fill: 拉伸图片填满容器，可能变形
   * - scale-down: contain和none中较小的一个
   * - none: 保持原始尺寸
   *
   * contain确保图片完整显示，不会被裁剪或变形
   */
  object-fit: contain;

  /*
   * transition: transform 0.3s - 过渡动画
   * 当transform属性改变时，用0.3秒时间平滑过渡
   *
   * transition语法：
   * transition: 属性名 持续时间 时间函数 延迟时间;
   *
   * 这里让缩放效果更加平滑，避免突兀的变化
   */
  transition: transform 0.3s;

  /*
   * transform: translateZ(0) - 3D变换
   * translateZ(0)将元素移动到3D空间的z=0平面
   * 主要目的是启用硬件加速，提升动画性能
   *
   * 硬件加速原理：
   * 浏览器会将该元素交给GPU处理，而不是CPU
   * GPU专门处理图形运算，性能更好
   */
  transform: translateZ(0);
}

/**
 * .parallax-overlay: 半透明遮罩层
 * 在背景图片上添加渐变遮罩，提升文字可读性
 */
.parallax-overlay {
  /* 绝对定位，覆盖整个父容器 */
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  /*
   * background: linear-gradient() - 线性渐变背景
   * 创建从一种颜色到另一种颜色的平滑过渡
   *
   * 语法：linear-gradient(方向, 颜色1, 颜色2, ...)
   *
   * 参数解析：
   * - to top: 渐变方向，从下到上
   * - rgba(0,0,0,0.5): 起始颜色，黑色50%透明度
   * - transparent: 结束颜色，完全透明
   *
   * 效果：底部较暗，顶部透明，让底部的文字更清晰可读
   *
   * 渐变方向选项：
   * - to top/bottom/left/right: 基本方向
   * - to top left: 对角线方向
   * - 45deg: 角度值（0deg向上，90deg向右）
   */
  background: linear-gradient(to top, rgba(0,0,0,0.5), transparent);

  /* z-index: 1 - 在背景图片之上，在文字内容之下 */
  z-index: 1;

  /* 保持与父容器相同的圆角 */
  border-radius: 8px;
}

/* 视差内容区样式 */
.parallax-content {
  position: absolute; /* 绝对定位 */
  bottom: 32rpx; /* 底部距离，根据需求设置 */
  left: 16px; /* 左侧距离 */
  display: flex; /* 弹性布局 */
  align-items: flex-end; /* 底部对齐 */
  z-index: 2; /* 在最上层 */
}

/* Logo图片样式 */
.logo {
  margin-right: 12px;  /* 右边距 */
  border: none;  /* 移除边框 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);  /* 增强阴影效果 */
  border-radius: 8px; /* 圆角8px */
  position: relative;
  z-index: 1; /* 确保在背景图之上 */
  padding: 3px;  /* 内边距 */
  background: rgba(255, 255, 255, 0.8);  /* 半透明白色背景 */
}

/* 标题文字样式 */
.title {
  font-size: 22px;  /* 字体大小 */
  font-weight: 600;  /* 字体粗细，相当于CSS的font-weight */
  color: #ffffff;  /* 白色文字 */
  letter-spacing: 1px;  /* 字符间距，相当于CSS的letter-spacing */
  /* 字体设置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  position: relative;
  z-index: 1; /* 确保在背景图之上 */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);  /* 文字阴影增强可读性 */

}

/* 联系信息容器样式 */
.contact-info-container {
  width: 100%; /* 与parallax-header保持一致的宽度 */
  margin-bottom: 4px;
  padding-top: 4px;
  padding-bottom: 4px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 联系信息行样式 */
.contact-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 4px; /* 减少底部外边距 */
  gap: 6px; /* 减少间距 */
  padding: 6px 10px 0 10px; /* 减少顶部内边距 */
}

/* 联系信息项样式 */
.contact-item {
  display: flex;
  align-items: flex-start;
  flex: 1;
  gap: 4px;
}

/* 联系信息图标样式 */
.contact-icon {
  color: #0052d9;
  font-size: 16px;
  margin-top: 2px;
  flex-shrink: 0;
}

/* 联系信息内容样式 */
.contact-content {
  position: relative;
  padding-right: 28px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* 联系信息标签样式 */
.contact-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
  font-weight: 500;
  flex-shrink: 0;
}

/* 联系信息值样式 */
.contact-value {
  font-size: 14px;
  color: #888888;
  font-weight: 500;
  line-height: 1.4;
  flex: 1;
}

/* 公告容器样式 */
.announcement-container {
  display: flex;
  align-items: flex-start;
  gap: 4px;
  padding: 6px 10px 6px 10px; /* 减少上下内边距 */
  border-top: 1px solid #f0f0f0;
  cursor: pointer; /* 添加手型光标 */
  transition: background-color 0.2s ease; /* 添加过渡效果 */
}

.announcement-container:active {
  background-color: #f5f5f5; /* 点击时的背景色 */
}

/* 公告图标样式 */
.announcement-icon {
  color: #ff6b35;
  font-size: 16px;
  margin-top: 2px;
  flex-shrink: 0;
}

/* 公告内容样式 */
.announcement-content {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* 公告标签样式 */
.announcement-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
  font-weight: 500;
}

/* 公告文本样式 */
.announcement-text {
  font-size: 14px;
  color: #888888;
  line-height: 1.5;
  text-align: justify;
}

/* 限制2行文字的公告文本样式 */
.announcement-text-limited {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  margin-right: 24px; /* 为更多按钮留出空间 */
  color: #888888;
}

/* 更多按钮样式 */
.announcement-more {
  position: absolute;
  right: 0;
  /* 让右箭头在多行文本的视觉中线居中 */
  top: 50%;
  transform: translateY(-50%) translateY(1em);
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  padding: 2px;
  z-index: 2;
}

/* 静态图片容器样式 */
.static-images-container {
  width: 100%;  /* 宽度100% */
  margin: 8px 0 20rpx 0;  /* 上边距使用px单位，更明显 */
  display: flex;  /* 弹性布局 */
  flex-direction: column;  /* 垂直排列 */
  gap: 16px;  /* 图片间距 */
}

/* 静态图片样式 */
.static-image-wrapper {
  width: 100%;
}

.static-image {
  width: 100%;
  /* 移除固定高度和 object-fit，保证高度自适应 */
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  display: block;
  background-color: #f5f5f5;
}

/* 功能按钮容器样式 */
.function-buttons {
  gap: 16px;  /* 元素间距，相当于CSS的gap */
  width: 100%;  /* 宽度100% */
  /* margin: 32rpx; */  /* 注释掉的边距 */
  margin-bottom: 32rpx;  /* 下边距，rpx是微信小程序的响应式单位 */
  display: flex;  /* 弹性布局 */
  justify-content: space-between;  /* 水平对齐方式，两端对齐 */
  /* 字体设置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.function-buttons.fixed-bottom {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  width: 100vw;
  max-width: 100vw;
  box-sizing: border-box;
  left: 0;
  right: 0;
  padding-left: 16px;
  padding-right: 16px;
}
.function-btn {
  flex: 1 1 0;
  min-width: 0;
  max-width: 100%;
}

/* 按钮内容样式，.t-button__content是TDesign组件的内部类名 */
.function-btn .t-button__content {
  height: 100%;  /* 高度100% */
  display: flex;  /* 弹性布局 */
  flex-direction: column;  /* 垂直排列 */
  justify-content: center;  /* 垂直居中 */
  align-items: center;  /* 水平居中 */
  gap: 8px;  /* 元素间距 */
}

/* 区块样式，用于课程区域 */
.section {
  margin-bottom: 24px;  /* 下边距 */
  /* 字体设置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 区块标题样式 */
.section-title {
  font-size: 18px;  /* 字体大小 */
  font-weight: 600;  /* 字体粗细 */
  margin-bottom: 16px;  /* 下边距 */
  color: #333;  /* 文字颜色 */
  /* 字体设置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 课程卡片容器样式 */
.course-cards {
  display: flex;  /* 弹性布局 */
  flex-direction: column;  /* 垂直排列 */
  gap: 16px;  /* 元素间距 */
}

/* 课程卡片样式 */
.course-card {
  background-color: #ffffff;  /* 背景色，白色 */
  border-radius: 12px;  /* 圆角 */
  padding: 16px;  /* 内边距 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);  /* 阴影效果 */
  /* 字体设置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 课程头部样式 */
.course-header {
  display: flex;  /* 弹性布局 */
  justify-content: space-between;  /* 两端对齐 */
  align-items: center;  /* 垂直居中 */
  margin-bottom: 12px;  /* 下边距 */
}

/* 课程标题样式 */
.course-title {
  font-size: 18px;  /* 字体大小 */
  font-weight: 600;  /* 字体粗细 */
  color: #333;  /* 文字颜色 */
  /* 字体设置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 课程状态样式 */
.course-status {
  padding: 4px 8px;  /* 内边距 */
  border-radius: 4px;  /* 圆角 */
  font-size: 12px;  /* 字体大小 */
  font-weight: 500;  /* 字体粗细 */
  /* 字体设置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  flex-shrink: 0;
  white-space: nowrap;
}

/* 可预约状态样式 */
.course-status.available {
  background-color: #e8f5e8;  /* 背景色，浅绿色 */
  color: #52c41a;  /* 文字颜色，绿色 */
}

/* 已满状态样式 */
.course-status.full {
  background-color: #fff2e8;  /* 背景色，浅橙色 */
  color: #fa8c16;  /* 文字颜色，橙色 */
}

/* 课程信息列表样式 */
.course-info-list {
  margin-bottom: 12px;  /* 下边距 */
}

/* 课程信息项样式 */
.course-info-item {
  display: flex;  /* 弹性布局 */
  align-items: center;  /* 垂直居中 */
  margin-bottom: 8px;  /* 下边距 */
  font-size: 14px;  /* 字体大小 */
  color: #666;  /* 文字颜色，灰色 */
  /* 字体设置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 课程信息项中的图标样式 */
.course-info-item t-icon {
  margin-right: 8px;  /* 右边距 */
  color: #0052d9;  /* 图标颜色，蓝色 */
}

/* 课程描述样式 */
.course-description {
  font-size: 14px;  /* 字体大小 */
  color: #666;  /* 文字颜色，灰色 */
  line-height: 1.5;  /* 行高，相当于CSS的line-height */
  margin-bottom: 16px;  /* 下边距 */
  /* 字体设置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 课程操作按钮容器样式 */
.course-actions {
  display: flex;  /* 弹性布局 */
  justify-content: flex-end;  /* 右对齐 */
}

/* 门店公告弹出层样式 */
.announcement-popup {
  --td-popup-border-radius: 20px;
}

.popup-content {
  background-color: #ffffff;
  border-radius: 20px;
  width: 80vw;
  max-width: 340px;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
  padding-bottom: 32px; /* 给下方留白 */
}

.popup-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 28px 24px 18px 24px;
  border-bottom: 1px solid #f0f0f0;
  gap: 10px;
}

.popup-title-icon {
  color: #faad14;
}

.indent-text {
  text-indent: 2em;
  display: block;
}

.popup-title {
  font-size: 20px;
  font-weight: 700;
  color: #333;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.close-btn {
  position: absolute;
  left: 50%;
  margin-left: -32rpx;
  bottom: -80rpx;
  cursor: pointer;
  transition: transform 0.2s ease;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0,0,0,0.10);
}

.close-btn:active {
  transform: scale(0.9);
}

.popup-body {
  padding: 28px 24px 0 24px;
  flex: 1;
  overflow-y: auto;
}

.popup-text {
  font-size: 17px;
  color: #333;
  line-height: 1.5;
  text-align: justify;
  word-break: break-all;
  white-space: pre-wrap;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.text-unify {
  font-size: 15px;
  font-weight: 500;
  color: #222;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.contact-label,
.contact-value,
.announcement-label,
.announcement-text,
.popup-title,
.popup-text {
  font-size: inherit;
  font-weight: inherit;
  color: inherit;
  font-family: inherit;
}

.contact-item-phone {
  flex: 1 1 0;
  min-width: 0;
}

.contact-item-address {
  flex: 2 1 0;
  min-width: 0;
}

.contact-value-limited {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  color: #888888;
}

.address-more {
  position: absolute;
  right: 0;
  /* 让箭头在多行文本的视觉中线居中（多行文本的垂直视觉居中） */
  top: 50%;
  transform: translateY(-50%) translateY(1em); /* 0.35em 可根据实际视觉微调 */
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  padding: 2px;
  z-index: 2;
}

.address-popup {
  --td-popup-border-radius: 20px;
}

.contact-divider {
  width: 1px;
  background: #e0e0e0;
  margin: 0 0 0 24rpx;
  align-self: stretch;
}

/* 相册popup美化升级 */
.album-popup {
  background: transparent !important;
}
.album-popup-content {
  background: transparent;
  border-radius: 0;
  width: 96vw;
  max-width: 100vw;
  max-height: 90vh;
  min-height: 0;
  box-shadow: none;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.album-popup-title {
  font-size: 18px;
  font-weight: 600;
  color: #222;
  margin-bottom: 12px;
  letter-spacing: 1px;
  text-align: center;
}
.album-popup-content swiper {
  width: 100vw;
  height: 90vh;
}
.album-popup-content swiper-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.album-popup-content image {
  width: 100vw;
  height: 90vh;
  object-fit: contain;
  background: transparent;
}
.album-popup-index {
  margin-top: 14px;
  font-size: 14px;
  color: #888;
  text-align: center;
  letter-spacing: 1px;
}
/* 关闭按钮（右上角悬浮） */
.album-popup .album-close-btn {
  position: absolute;
  top: 14px;
  right: 14px;
  z-index: 10001;
  width: auto;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255,255,255,0.92);
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  font-size: 15px;
  color: #333;
  padding: 0 12px 0 4px;
  cursor: pointer;
}
.album-popup .album-close-btn t-icon {
  margin-right: 4px;
}
.album-popup-close {
  position: absolute;         /* 绝对定位，固定在弹窗右上角 */
  top: 18px;
  right: 24px;
  z-index: 10;
  width: 48px;                /* 按钮宽高，圆形 */
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(30, 30, 30, 0.65); /* 深色半透明背景，提升高级感（增加不透明度替代毛玻璃效果） */
  border-radius: 50%;         /* 圆形按钮 */
  box-shadow: 0 4px 16px rgba(0,0,0,0.18); /* 阴影，增强立体感 */
  /* backdrop-filter: blur(6px); 毛玻璃效果在小程序中可能不支持，已移除 */
  transition: background 0.2s, box-shadow 0.2s;
  cursor: pointer;
}
.album-popup-close:active {
  background: rgba(30, 30, 30, 0.7);  /* 按下时背景更深，提升交互感 */
  box-shadow: 0 2px 8px rgba(0,0,0,0.28);
}
.album-popup-close t-icon {
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.25)); /* 图标阴影，保证白色图标在浅色图片上也清晰 */
}

.contact-value,
.contact-value-limited,
.announcement-text,
.announcement-text-limited {
  color: #888888 !important;
  font-size: 15px !important;
}

.contact-label,
.announcement-label {
  font-size: 15px !important;
}

/* 骨架屏样式 */
/*
 * 骨架屏容器
 * 模拟整个页面的布局结构
 * 与真实内容保持一致的间距和布局
 *
 * 修复出界问题：
 * 1. 确保容器不超出屏幕边界
 * 2. 使用合适的内边距
 * 3. 限制最大宽度
 */
.skeleton-container {
  width: 100%;
  max-width: 100vw; /* 限制最大宽度，防止出界 */
  padding: 16px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden; /* 防止子元素出界 */
}

/*
 * 骨架屏基础样式
 * 使用渐变动画模拟加载状态
 *
 * 渐变动画原理：
 * 1. 创建一个左右渐变的背景，模拟光线扫过的效果
 * 2. 设置背景尺寸为400%，使渐变效果更明显
 * 3. 通过动画移动背景位置，产生"扫光"效果
 * 4. 使用ease-in-out缓动函数，让动画更自然
 *
 * 颜色选择：
 * - #f0f0f0: 基础灰色，模拟未加载状态
 * - #e0e0e0: 中间色，产生渐变过渡
 * - #f0f0f0: 回到基础色，形成循环
 *
 * 与其他技术对比：
 * - React: 可以用CSS-in-JS或styled-components实现
 * - Vue: 可以用scoped CSS或CSS modules实现
 * - Android: 可以用ShimmerFrameLayout实现类似效果
 * - iOS: 可以用CAGradientLayer实现渐变动画
 */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 37%, #f0f0f0 63%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.5s infinite ease-in-out;
  border-radius: 6px;
  position: relative;
  overflow: hidden;
}

/*
 * 骨架屏动画关键帧
 *
 * 动画效果说明：
 * - 0%: 渐变从右侧开始（100% 50%）
 * - 100%: 渐变移动到左侧（0 50%）
 * - infinite: 无限循环播放
 * - ease-in-out: 缓入缓出，动画更自然
 *
 * 性能优化：
 * - 使用transform代替background-position可以获得更好的性能
 * - 但background-position在小程序中兼容性更好
 */
@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}

/*
 * 骨架屏淡入动画
 * 让骨架屏出现时有淡入效果，避免突兀
 */
.skeleton-container {
  animation: skeleton-fade-in 0.3s ease-out;
}

@keyframes skeleton-fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* 头部骨架屏样式 */
.skeleton-header {
  width: 100%;
  max-width: 100%; /* 确保不超出父容器 */
  height: 200px;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 16px;
  box-sizing: border-box; /* 包含边框和内边距在宽度内 */
}

/* 背景图骨架 */
.skeleton-bg {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  box-sizing: border-box;
}

/* Logo和标题容器 */
.skeleton-content {
  position: absolute;
  bottom: 16px; /* 使用px单位，更稳定 */
  left: 16px;
  right: 16px; /* 添加右边界，防止出界 */
  display: flex;
  align-items: flex-end;
  z-index: 2;
  box-sizing: border-box;
}

/* Logo骨架 */
.skeleton-logo {
  width: 60px;
  height: 60px;
  min-width: 60px; /* 防止压缩 */
  border-radius: 50%;
  margin-right: 12px;
  flex-shrink: 0; /* 防止在flex布局中被压缩 */
}

/* 标题骨架 */
.skeleton-title {
  width: 120px;
  max-width: calc(100% - 72px); /* 减去Logo宽度和间距，防止出界 */
  height: 24px;
  border-radius: 4px;
  flex-shrink: 0; /* 防止被压缩 */
}

/* 联系信息骨架 */
.skeleton-contact {
  width: 100%;
  max-width: 100%; /* 防止出界 */
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  box-sizing: border-box; /* 包含内边距在宽度内 */
}

/* 联系信息项 */
.skeleton-contact-item {
  display: flex;
  align-items: center;
  width: 100%;
  max-width: 100%; /* 防止出界 */
  box-sizing: border-box;
}

/* 图标骨架 */
.skeleton-icon {
  width: 24px;
  height: 24px;
  min-width: 24px; /* 防止压缩 */
  border-radius: 50%;
  margin-right: 12px;
  flex-shrink: 0; /* 防止被压缩 */
}

/* 联系信息内容 */
.skeleton-contact-content {
  flex: 1;
  min-width: 0; /* 允许flex项目缩小 */
  display: flex;
  flex-direction: column;
  gap: 8px;
  overflow: hidden; /* 防止内容出界 */
}

/* 短文本骨架（标签） */
.skeleton-text-short {
  width: 60px;
  max-width: 100%; /* 防止出界 */
  height: 16px;
}

/* 长文本骨架（内容） */
.skeleton-text-long {
  width: 90%;
  max-width: 100%; /* 防止出界 */
  height: 16px;
}

/* 图片展示区骨架 */
.skeleton-images {
  width: 100%;
  max-width: 100%; /* 防止出界 */
  display: flex;
  flex-direction: column;
  gap: 16px;
  box-sizing: border-box;
}

/* 图片骨架 */
.skeleton-image {
  width: 100%;
  max-width: 100%; /* 防止出界 */
  height: 180px;
  border-radius: 12px;
  box-sizing: border-box;
}

/* 按钮区域骨架 */
.skeleton-buttons {
  width: 100%;
  max-width: 100%; /* 防止出界 */
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 8px;
  box-sizing: border-box;
}

/* 按钮骨架 */
.skeleton-button {
  width: 100%;
  max-width: 100%; /* 防止出界 */
  height: 48px;
  border-radius: 8px;
  box-sizing: border-box;
}

/*
 * 响应式设计
 * 针对不同屏幕尺寸优化骨架屏显示效果
 *
 * 小程序屏幕适配说明：
 * - 使用rpx单位实现响应式设计
 * - rpx会根据屏幕宽度自动缩放
 * - 750rpx = 屏幕宽度（无论实际像素多少）
 */

/* 小屏幕设备优化（如iPhone SE） */
@media (max-width: 375px) {
  .skeleton-container {
    padding: 12px; /* 减小内边距，节省空间 */
  }

  .skeleton-header {
    height: 160px; /* 减小头部高度 */
  }

  .skeleton-logo {
    width: 50px;   /* 减小Logo尺寸 */
    height: 50px;
    min-width: 50px; /* 更新最小宽度 */
  }

  .skeleton-title {
    width: 100px;  /* 减小标题宽度 */
    max-width: calc(100% - 62px); /* 更新最大宽度 */
    height: 20px;  /* 减小标题高度 */
  }

  .skeleton-image {
    height: 150px; /* 减小图片高度 */
  }

  .skeleton-contact {
    padding: 12px; /* 减小内边距 */
  }
}

/* 大屏幕设备优化（如iPhone Plus系列） */
@media (min-width: 414px) {
  .skeleton-container {
    padding: 20px; /* 增大内边距，利用更多空间 */
  }

  .skeleton-header {
    height: 240px; /* 增大头部高度 */
  }

  .skeleton-logo {
    width: 70px;   /* 增大Logo尺寸 */
    height: 70px;
    min-width: 70px; /* 更新最小宽度 */
  }

  .skeleton-title {
    width: 140px;  /* 增大标题宽度 */
    max-width: calc(100% - 82px); /* 更新最大宽度 */
    height: 28px;  /* 增大标题高度 */
  }

  .skeleton-image {
    height: 200px; /* 增大图片高度 */
  }

  .skeleton-contact {
    padding: 20px; /* 增大内边距 */
  }
}

/*
 * 深色模式支持（预留）
 * 小程序暂时不支持系统深色模式检测
 * 但可以预留样式，便于未来扩展
 */
.skeleton-container.dark-mode .skeleton {
  background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 37%, #2a2a2a 63%);
}

/*
 * 高对比度模式支持（无障碍设计）
 * 为视觉障碍用户提供更好的体验
 */
.skeleton-container.high-contrast .skeleton {
  background: linear-gradient(90deg, #000000 25%, #333333 37%, #000000 63%);
  border: 1px solid #666666;
}

/*
 * 减少动画模式（为动画敏感用户提供选项）
 * 注意：prefers-reduced-motion 在微信小程序中可能不被支持
 * 如需要可以通过JavaScript动态添加类名来控制
 *
 * 使用方法：
 * 1. 在JavaScript中检测用户偏好
 * 2. 动态添加 .reduced-motion 类名到容器
 * 3. 使用以下样式禁用动画
 */
.skeleton-container.reduced-motion .skeleton {
  animation: none; /* 禁用动画 */
  background: #f0f0f0; /* 使用静态背景色 */
}

.skeleton-container.reduced-motion {
  animation: none; /* 禁用淡入动画 */
}

/*
 * 额外的安全措施
 * 确保骨架屏在所有情况下都不会出界
 */

/*
 * 全局防出界设置
 * 由于WXSS不支持通配符选择器，需要为每个具体的类设置
 */
.skeleton-container view,
.skeleton-container .skeleton,
.skeleton-container .skeleton-header,
.skeleton-container .skeleton-content,
.skeleton-container .skeleton-bg,
.skeleton-container .skeleton-logo,
.skeleton-container .skeleton-title,
.skeleton-container .skeleton-contact,
.skeleton-container .skeleton-contact-item,
.skeleton-container .skeleton-contact-content,
.skeleton-container .skeleton-icon,
.skeleton-container .skeleton-text-short,
.skeleton-container .skeleton-text-long,
.skeleton-container .skeleton-images,
.skeleton-container .skeleton-image,
.skeleton-container .skeleton-buttons,
.skeleton-container .skeleton-button {
  max-width: 100%; /* 所有子元素都不能超出父容器 */
  box-sizing: border-box; /* 统一使用border-box模型 */
}

/* 防止水平滚动 */
.skeleton-container {
  overflow-x: hidden; /* 隐藏水平滚动条 */
}

/* 确保flex容器不会导致出界 */
.skeleton-container .skeleton-content,
.skeleton-container .skeleton-contact-item,
.skeleton-container .skeleton-contact-content {
  min-width: 0; /* 允许flex项目缩小到内容大小以下 */
  overflow: hidden; /* 隐藏溢出内容 */
}

/* 文本骨架的安全边界 */
.skeleton-text-short,
.skeleton-text-long {
  min-width: 20px; /* 设置最小宽度，避免过小 */
  max-width: calc(100% - 10px); /* 留出安全边距 */
}

/* 图标骨架的固定尺寸 */
.skeleton-icon,
.skeleton-logo {
  flex-shrink: 0; /* 永远不压缩 */
  overflow: hidden; /* 隐藏可能的溢出 */
}

/*
 * 开发工具样式
 * 用于调试和演示骨架屏效果
 */
.dev-tools {
  position: fixed;
  bottom: 100px;
  right: 16px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  padding: 12px;
  z-index: 9999;
  min-width: 120px;
}

.dev-tools-title {
  color: #ffffff;
  font-size: 12px;
  text-align: center;
  margin-bottom: 8px;
  font-weight: 600;
}

.dev-tools-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.dev-btn {
  font-size: 12px !important;
  height: 32px !important;
  padding: 0 8px !important;
}

/*
 * 开发工具触发器
 * 隐藏的触发区域，长按可显示开发工具
 */
.dev-trigger {
  position: fixed;
  top: 0;
  left: 0;
  width: 50px;
  height: 50px;
  opacity: 0;
  z-index: 1;
  /* 完全透明，用户看不到但可以触发 */
}