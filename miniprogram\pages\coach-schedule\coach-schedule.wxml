<view class="container">
  <!-- 视图切换栏 -->
  <view class="view-section">
    <view class="booking-tabs">
      <view 
        class="booking-tab {{activeView === item.value ? 'active' : ''}}"
        wx:for="{{viewTabs}}" 
        wx:key="value"
        bind:tap="onViewChange"
        data-value="{{item.value}}"
      >
        {{item.label}}
      </view>
    </view>
  </view>

  <!-- 横向日期选择器 - 仅在当前课程界面显示 -->
  <scroll-view wx:if="{{activeView === 'current'}}" class="date-tabs-scroll" scroll-x="true">
    <view class="date-tab"
          wx:for="{{dateTabs}}"
          wx:key="value"
          data-value="{{item.value}}"
          bindtap="onDateTabChange"
          style="color:{{selectedDate === item.value ? '#222' : '#bbb'}};font-weight:{{selectedDate === item.value ? 'bold' : 'normal'}}">
      <view class="tab-label">{{item.label}}</view>
      <view class="tab-date" wx:if="{{item.date}}">{{item.date}}</view>
      <view wx:if="{{selectedDate === item.value}}" style="height:3px;background:#0052d9;border-radius:2px;margin-top:2px;"></view>
    </view>
  </scroll-view>

  <!-- 课程列表 -->
  <view class="course-list">
    <!-- 历史课程tab - 使用分页加载 -->
    <view wx:if="{{activeView === 'history' && historyCourses.length === 0 && !historyLoading}}" style="flex: 1; display: flex; align-items: center; justify-content: center;">
      <t-empty description="{{emptyDescription}}" />
    </view>
    <scroll-view
      wx:if="{{activeView === 'history' && (historyCourses.length > 0 || historyLoading)}}"
      scroll-y="true"
      style="flex: 1; min-height: 0;"
      bindscrolltolower="onReachBottom"
    >
      <block wx:for="{{historyCourses}}" wx:key="id">
        <!-- 日期分组分隔条 -->
        <view wx:if="{{index === 0 || historyCourses[index-1].date !== item.date}}" class="timeline-date">{{item.date}}</view>
        <view class="course-card" bind:tap="onCourseTap" data-course="{{item}}">
          <view class="course-header">
            <view class="course-title">{{item.name}}</view>
            <view class="course-status ended">已结束</view>
          </view>
          <view class="course-info-list">
            <view class="info-item">
              <t-icon name="time" size="16" />
              <text>{{item.formattedDate}} {{item.time}}</text>
            </view>
            <view class="info-item">
              <t-icon name="user" size="16" />
              <text>{{item.coach}}</text>
            </view>
            <view class="info-item">
              <t-icon name="location" size="16" />
              <text>{{item.venue}}</text>
            </view>
            <view class="info-item">
              <t-icon name="chart" size="16" />
              <text>剩余名额：{{item.remaining}}/{{item.capacity}}</text>
            </view>
          </view>
        </view>
      </block>
      <view wx:if="{{historyLoading}}" class="loading-indicator">加载中...</view>
      <view wx:elif="{{!historyHasMore && historyCourses.length > 0}}" class="end-indicator">没有更多了</view>
    </scroll-view>

    <!-- 当前课程tab - 也使用scroll-view以保持一致性 -->
    <view wx:if="{{activeView === 'current' && filteredCourseList.length === 0 && !loading}}" style="flex: 1; display: flex; align-items: center; justify-content: center;">
      <t-empty description="{{emptyDescription}}" />
    </view>
    <scroll-view
      wx:if="{{activeView === 'current' && (filteredCourseList.length > 0 || loading)}}"
      scroll-y="true"
      style="flex: 1; min-height: 0;"
      bindscrolltolower="onCurrentReachBottom"
    >
      <view wx:if="{{filteredCourseList.length > 0}}">
        <view class="course-card" wx:for="{{filteredCourseList}}" wx:key="_id" bindtap="onCourseTap" data-course="{{item}}">
          <view class="course-header">
            <view class="course-title">{{item.name}}</view>
          </view>
          <view class="course-info-list">
            <view class="info-item">
              <t-icon name="time" size="16" />
              <text>{{item.date}} {{item.time}}</text>
            </view>
            <view class="info-item">
              <t-icon name="location" size="16" />
              <text>{{item.venue}}</text>
            </view>
            <view class="info-item">
              <t-icon name="chart" size="16" />
              <text>剩余名额：{{item.capacity - item.bookedCount}}/{{item.capacity}}</text>
            </view>
            <view class="info-item">
              <t-icon name="help-circle" size="16" />
              <text class="activity-detail">活动详情：{{item.activityDetail.description}}</text>
            </view>
          </view>
          <!-- 已预约学员列表等原有内容保留 -->
          <view class="booked-students-section" catchtap="onStudentSectionTap">
            <view class="collapse-header" catchtap="toggleCollapse" data-course-id="{{item._id}}">
              <text>已预约学员（{{item.bookedStudents ? item.bookedStudents.length : 0}}人）</text>
              <t-icon name="{{item.collapseValue && item.collapseValue.length > 0 && item.collapseValue.indexOf('students') !== -1 ? 'chevron-up' : 'chevron-down'}}" size="16" />
            </view>
            <view class="collapse-content" wx:if="{{item.collapseValue && item.collapseValue.length > 0 && item.collapseValue.indexOf('students') !== -1}}">
              <view class="student-list">
                <view wx:if="{{!item.bookedStudents || item.bookedStudents.length === 0}}" class="no-students">
                  暂无预约学员
                </view>
                <view wx:else>
                  <view class="student-item" wx:for="{{item.bookedStudents}}" wx:key="openid" wx:for-item="student">
                    <t-icon name="user" size="14" />
                    <text class="student-name">{{student.nickName || student.name || '未知用户'}}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <view class="course-footer"></view>
        </view>
      </view>
      <view wx:if="{{loading}}" class="loading-indicator">加载中...</view>
    </scroll-view>
  </view>
  <t-dialog visible="{{showDetailDialog}}" title="课程详情" bind:close="onDialogClose">
    <!-- 详情弹窗内容可后续补充 -->
  </t-dialog>
  <t-toast id="t-toast" />
  <view id="tab-bar-placeholder"></view>
</view> 