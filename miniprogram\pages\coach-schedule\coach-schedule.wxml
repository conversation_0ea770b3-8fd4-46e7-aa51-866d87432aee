<!--
  讲师课表页面结构文件

  页面功能：
  1. 课程展示：按状态分类显示讲师的所有课程
  2. 多维度筛选：按状态、时间筛选课程
  3. 分页加载：支持大量课程记录的性能优化
  4. 课程管理：查看课程详情、学员管理等
  5. 实时同步：与数据库保持数据同步

  架构设计：
  采用与 my-bookings 页面相同的 TDesign 组件架构和布局方式
  使用 t-tabs 组件实现顶部Tab切换
  使用 scroll-view 实现分页加载
  使用 t-empty 组件显示空状态
  使用 t-icon 组件显示图标
-->

<view class="container">
  <!-- 顶部Tab切换区域 - 采用 schedule 页面的设计风格 -->
  <view class="view-section">
    <!--
      顶部选项卡区域容器 - 完全照搬 schedule 页面的设计
      使用与 schedule 页面相同的样式设计，包括渐变背景、边框、阴影等
    -->
    <view class="top-tabs-section">
      <!--
        TDesign线条选项卡组件 - 与 schedule 页面保持完全一致

        属性说明：
        - value: 当前激活的选项卡值，对应activeTab数据
        - bind:change: 选项卡切换事件，调用onTabChange方法
        - theme: 选项卡主题，"line"表示线条式选项卡
        - show-bottom-line: 是否显示底部分割线
        - t-class: 自定义样式类名，使用custom-top-tabs样式

        与 schedule 页面的区别：
        - schedule: "活动表"、"当前活动"、"历史活动"
        - coach-schedule: "当前课程"、"历史课程"
      -->
      <t-tabs
        value="{{activeTab}}"
        bind:change="onTabChange"
        theme="line"
        show-bottom-line="{{true}}"
        t-class="custom-top-tabs"
      >
        <!--
          选项卡面板：定义具体的tab项
          t-tab-panel: TDesign的选项卡面板组件

          保持讲师课表的业务逻辑，但采用 schedule 页面的视觉设计
        -->
        <t-tab-panel label="当前课程" value="current"></t-tab-panel>
        <t-tab-panel label="历史课程" value="history"></t-tab-panel>
      </t-tabs>
    </view>
  </view>

  <!-- 横向日期选择器 - 采用 schedule 页面的时间选择器设计风格 -->
  <view wx:if="{{activeTab === 'current'}}" class="date-filter-section">
    <!--
      日期筛选器容器 - 与 schedule 页面的搜索操作区域保持一致的设计风格
      包括渐变背景、精致边框、轻微阴影等视觉效果
    -->
    <view class="date-filter-container">
      <!--
        横向滚动的日期选择器
        采用 schedule 页面的卡片式设计风格
        每个日期项都是一个独立的卡片，有选中状态的视觉反馈
      -->
      <scroll-view class="date-tabs-scroll" scroll-x="true" show-scrollbar="{{false}}">
        <view class="date-tabs-container">
          <view
            class="date-tab {{selectedDate === item.value ? 'active' : ''}}"
            wx:for="{{dateTabs}}"
            wx:key="value"
            data-value="{{item.value}}"
            bindtap="onDateTabChange"
          >
            <!-- 日期标签 -->
            <view class="date-tab-label">{{item.label}}</view>
            <!-- 日期数字 -->
            <view class="date-tab-date" wx:if="{{item.date}}">{{item.date}}</view>
            <!-- 选中状态指示器 -->
            <view wx:if="{{selectedDate === item.value}}" class="date-tab-indicator"></view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 当前课程列表 - 采用与my-bookings相同的结构 -->
  <view class="booking-list" wx:if="{{activeTab === 'current'}}">
    <!--
      t-empty组件说明：
      - 这是TDesign提供的空状态组件
      - 用于显示无数据时的友好提示
      - 与HTML的<div class="empty-state">类似，但提供标准化的空状态设计
      - description属性用于设置空状态描述文字
    -->
    <t-empty wx:if="{{filteredCurrentCourses.length === 0}}" description="暂无当前课程" />
    <!--
      scroll-view组件说明：
      - 可滚动视图容器，支持上拉加载和下拉刷新
      - scroll-y="true" 表示支持纵向滚动
      - bindscrolltolower 绑定滚动到底部的事件处理函数
      - style="flex:1;min-height:0;" 确保容器占满剩余空间
    -->
    <scroll-view wx:else scroll-y="true" style="flex:1;min-height:0;"
      bindscrolltolower="onCurrentScrollToLower"
    >
      <!--
        block组件说明：
        - 这是小程序提供的逻辑组件，不会在页面中渲染
        - 用于包装列表渲染逻辑
        - 与HTML的<template>标签类似，但专为小程序优化
        - wx:for 用于列表渲染，wx:key 用于提高渲染性能
      -->
      <block wx:for="{{filteredCurrentCourses}}" wx:key="id">
        <!-- 时间轴分组：如果是新日期，显示日期分隔 -->
        <!--
          条件渲染说明：
          - wx:if 用于条件渲染，类似于JavaScript的if语句
          - 这里判断是否为新日期，如果是则显示时间轴分隔条
          - 时间轴设计提供更好的时间维度视觉分组
        -->
        <view wx:if="{{index === 0 || filteredCurrentCourses[index-1].date !== item.date}}" class="timeline-date">{{item.date}}</view>
        <!--
          课程卡片组件：
          - 每个课程显示为一个独立的卡片
          - bind:tap 绑定点击事件处理函数
          - data-course 传递课程数据给事件处理函数
          - class 设置CSS类名，支持动画效果
        -->
        <view
          class="booking-card{{flashIndexes.indexOf(index) !== -1 ? ' slide-in' : ''}}"
          bind:tap="viewCourseDetail"
          data-course="{{item}}"
        >
          <!-- 课程状态标签 -->
          <view class="booking-status {{item.status}}">{{item.statusText}}</view>
          <!-- 课程标题 -->
          <view class="course-title">{{item.courseName}}</view>
          <view class="course-header" style="display:flex;justify-content:space-between;align-items:center;">
            <view></view>
          </view>
          <!-- 课程信息列表 -->
          <view class="course-info-list">
            <!-- 时间信息 -->
            <view class="info-item">
              <!--
                t-icon组件说明：
                - TDesign提供的图标组件
                - name属性指定图标名称
                - size属性设置图标大小（单位：px）
                - 提供统一的图标设计风格
              -->
              <t-icon name="time" size="16" />
              <text>{{item.date}} {{item.time}}</text>
            </view>
            <!-- 讲师信息 -->
            <view class="info-item">
              <t-icon name="user" size="16" />
              <text>{{item.coach}}</text>
            </view>
            <!-- 地点信息 -->
            <view class="info-item">
              <t-icon name="location" size="16" />
              <text>{{item.venue}}</text>
            </view>
            <!-- 名额信息 -->
            <view class="info-item">
              <t-icon name="chart" size="16" />
              <text>剩余名额：{{item.remaining}}/{{item.capacity}}</text>
            </view>
          </view>
        </view>
      </block>
      <!-- 加载状态提示 -->
      <view wx:if="{{isLoadingBottom}}" class="loading-indicator">加载中...</view>
      <view wx:elif="{{noMoreCurrent}}" class="end-indicator">到底啦！</view>
    </scroll-view>
  </view>

  <!-- 历史课程列表 - 采用与my-bookings相同的结构 -->
  <view class="booking-list" wx:if="{{activeTab === 'history'}}">
    <t-empty wx:if="{{filteredHistoryCourses.length === 0}}" description="暂无历史课程" />
    <scroll-view wx:else scroll-y="true" style="flex:1;min-height:0;"
      bindscrolltolower="onHistoryScrollToLower"
      bindscrolltoupper="onHistoryScrollToUpper"
      refresher-enabled="true"
      refresher-triggered="{{isRefresherTriggered}}"
      bindrefresherrefresh="onRefresherRefresh"
      bindrefresherpulling="onRefresherPulling"
      bindrefresherabort="onRefresherAbort"
    >
      <!-- 顶部加载提示 -->
      <view wx:if="{{isLoadingTop}}" class="loading-indicator">加载历史中...</view>
      <view wx:elif="{{noMoreHistory && isPullingDown}}" class="end-indicator">到顶啦！</view>

      <block wx:for="{{visibleHistoryCourses}}" wx:key="id">
        <!-- 时间轴分组：如果是新日期，显示日期分隔 -->
        <view wx:if="{{index === 0 || visibleHistoryCourses[index-1].date !== item.date}}" class="timeline-date">{{item.date}}</view>
        <view
          class="booking-card{{flashIndexesHistory.indexOf(index) !== -1 ? ' slide-in' : ''}}"
          bind:tap="viewCourseDetail"
          data-course="{{item}}"
        >
          <!-- 课程状态标签 -->
          <view class="booking-status {{item.status}}">{{item.statusText}}</view>
          <!-- 课程标题 -->
          <view class="course-title">{{item.courseName}}</view>
          <view class="course-header" style="display:flex;justify-content:space-between;align-items:center;">
            <view></view>
          </view>
          <!-- 课程信息列表 -->
          <view class="course-info-list">
            <!-- 时间信息 -->
            <view class="info-item">
              <t-icon name="time" size="16" />
              <text>{{item.date}} {{item.time}}</text>
            </view>
            <!-- 讲师信息 -->
            <view class="info-item">
              <t-icon name="user" size="16" />
              <text>{{item.coach}}</text>
            </view>
            <!-- 地点信息 -->
            <view class="info-item">
              <t-icon name="location" size="16" />
              <text>{{item.venue}}</text>
            </view>
            <!-- 名额信息 -->
            <view class="info-item">
              <t-icon name="chart" size="16" />
              <text>剩余名额：{{item.remaining}}/{{item.capacity}}</text>
            </view>
          </view>

          <!-- 已预约学员列表 - 保持原有功能 -->
          <view class="booked-students-section" catchtap="onStudentSectionTap">
            <view class="collapse-header" catchtap="toggleCollapse" data-course-id="{{item._id}}">
              <text>已预约学员（{{item.bookedStudents ? item.bookedStudents.length : 0}}人）</text>
              <t-icon name="{{item.collapsed ? 'chevron-down' : 'chevron-up'}}" size="16" />
            </view>
            <view class="collapse-content" wx:if="{{!item.collapsed}}">
              <view class="student-list" wx:if="{{item.bookedStudents && item.bookedStudents.length > 0}}">
                <view class="student-item" wx:for="{{item.bookedStudents}}" wx:key="openid" wx:for-item="student">
                  <t-icon name="user" size="14" />
                  <text class="student-name">{{student.nickName || '匿名学员'}}</text>
                </view>
              </view>
              <view class="no-students" wx:else>
                暂无学员预约
              </view>
            </view>
          </view>
        </view>
      </block>

      <!-- 底部加载提示 -->
      <view wx:if="{{isLoadingBottom}}" class="loading-indicator">加载中...</view>
      <view wx:elif="{{noMoreHistory}}" class="end-indicator">到底啦！</view>
    </scroll-view>
  </view>

  <!-- Toast组件 - 用于显示提示信息 -->
  <!--
    t-toast组件说明：
    - TDesign提供的轻提示组件
    - 用于显示操作反馈、错误提示等短暂信息
    - id="t-toast" 用于在JavaScript中调用显示方法
  -->
  <t-toast id="t-toast" />
</view>