# 登录跳转修复说明

## 问题描述

在schedule页面中，当用户未登录时点击预约按钮，会弹出登录确认弹窗。用户点击"去登录"按钮后，弹窗关闭但没有跳转到登录页面。

## 问题原因

**错误代码**：
```javascript
wx.navigateTo({ url: '/pages/profile/profile' });
```

**问题分析**：
- `profile` 页面是一个 tabBar 页面（在 app.json 中配置的底部导航页面）
- 对于 tabBar 页面，必须使用 `wx.switchTab` 而不是 `wx.navigateTo`
- `wx.navigateTo` 只能用于跳转普通页面，不能跳转 tabBar 页面

## 修复方案

**修复后的代码**：
```javascript
wx.switchTab({ url: '/pages/profile/profile' });
```

### 修复位置

**文件**: `miniprogram/pages/schedule/schedule.js`
**行数**: 652
**函数**: `bookCourse`

### 微信小程序页面跳转API说明

| API | 用途 | 适用页面类型 | 是否可返回 |
|-----|------|-------------|-----------|
| `wx.switchTab` | 跳转到 tabBar 页面 | tabBar 页面 | 否（会清空页面栈） |
| `wx.navigateTo` | 跳转到普通页面 | 非 tabBar 页面 | 是 |
| `wx.redirectTo` | 重定向到页面 | 非 tabBar 页面 | 否（替换当前页面） |
| `wx.reLaunch` | 重启应用到指定页面 | 任意页面 | 否（清空所有页面栈） |

### tabBar 页面识别

在 `app.json` 中配置的 tabBar 页面：
```json
{
  "tabBar": {
    "list": [
      { "pagePath": "pages/index/index" },
      { "pagePath": "pages/schedule/schedule" },
      { "pagePath": "pages/profile/profile" }
    ]
  }
}
```

## 修复验证

### 测试步骤

1. **未登录状态测试**：
   - 确保用户处于未登录状态
   - 进入 schedule 页面
   - 点击任意课程的"预约"按钮

2. **弹窗测试**：
   - 应该弹出"未登录"提示弹窗
   - 弹窗内容：`您还未登录，是否前往登录？`
   - 按钮：`去登录` 和 `取消`

3. **跳转测试**：
   - 点击"去登录"按钮
   - 应该成功跳转到 profile 页面（个人中心）
   - 底部 tabBar 应该高亮显示"我的"选项

### 预期结果

- ✅ 弹窗正常显示
- ✅ 点击"去登录"后弹窗关闭
- ✅ 成功跳转到 profile 页面
- ✅ tabBar 状态正确更新

## 相关页面检查

已检查其他页面的类似跳转，确认没有相同问题：

- ✅ `course-detail.js` - 使用正确的 `wx.switchTab`
- ✅ `index.js` - 使用正确的 `wx.switchTab`
- ✅ 其他页面 - 没有跳转到 profile 页面的登录逻辑

## 技术要点

### 1. tabBar 页面特性
- tabBar 页面是小程序的主要导航页面
- 只能通过 `wx.switchTab` 进行跳转
- 跳转时会清空页面栈中的非 tabBar 页面

### 2. 错误处理
- 使用错误的跳转API不会报错，但跳转会失败
- 用户看到的现象是点击无效果

### 3. 最佳实践
- 跳转前确认目标页面类型
- tabBar 页面使用 `wx.switchTab`
- 普通页面使用 `wx.navigateTo`

## 影响范围

- **修复页面**: schedule 页面
- **影响功能**: 未登录用户的登录跳转
- **用户体验**: 提升了登录流程的流畅性
- **兼容性**: 无影响，向后兼容
