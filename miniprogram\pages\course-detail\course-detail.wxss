/* course-detail.wxss */
/* Generated by <PERSON> to fix layout issues */

/* ---- Generic Layout ---- */
.page-container {
  background-color: #f8f8f8;
  min-height: 100vh;
  padding-bottom: 180rpx; /* 为底部操作栏留出空间 */
}

/* ---- Loading & Error States ---- */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f5f5f5;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  padding: 32px;
}

.error-icon-placeholder {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
  margin-bottom: 32px;
}

.error-message {
  font-size: 16px;
  color: #666;
  margin-bottom: 32px;
}

/* ---- Hero Section (Header) ---- */
.hero-section {
  position: relative;
  height: 400rpx;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 32rpx;
  box-sizing: border-box;
  color: #ffffff;
}

.hero-background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.1));
  z-index: 2;
}

.hero-content {
  position: relative;
  z-index: 3;
}

.course-title-main {
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

.course-tags {
  display: flex;
  gap: 16rpx;
}

.tag-item {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  background-color: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.tag-icon {
  margin-right: 8rpx;
}

.status-available { background-color: rgba(0, 168, 112, 0.7); }
.status-booked { background-color: rgba(0, 82, 217, 0.7); }
.status-full { background-color: rgba(227, 77, 89, 0.7); }
.status-ended { background-color: rgba(153, 153, 153, 0.7); }

/* ---- Main Content Area ---- */
.main-content {
  padding: 32rpx;
  margin-top: -40rpx; /* Overlap with hero section */
  position: relative;
  z-index: 4;
}

/* ---- Info Cards ---- */
.info-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  gap: 32rpx;
  margin-bottom: 32rpx;
}

.info-item-large {
  display: flex;
  align-items: center;
}

.info-icon-large {
  color: #0052d9;
  margin-right: 24rpx;
}

.info-text-large {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 4rpx;
}

.info-value {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.content-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 32rpx;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 24rpx;
  border-bottom: 1px solid #f0f0f0;
}

.card-header-icon {
  color: #0052d9;
  margin-right: 16rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.card-body {
  font-size: 28rpx;
  color: #666;
  line-height: 1.7;
}

.description-text {
  white-space: pre-wrap;
}

/* ---- Coach Section ---- */
.coach-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.coach-item {
  display: flex;
  align-items: center;
}

.coach-info {
  margin-left: 24rpx;
}

.coach-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.coach-specialty {
  font-size: 24rpx;
  color: #999;
}

/* ---- Booking Info Section ---- */
.booking-info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  text-align: center;
  margin-bottom: 24rpx;
}

.booking-info-item .booking-info-value {
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.booking-info-item .booking-info-label {
  font-size: 24rpx;
  color: #999;
}

.remaining-yes .booking-info-value { color: #00a870; }
.remaining-no .booking-info-value { color: #e34d59; }

.cancel-policy {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
  margin-top: 12px;
}

.cancel-policy-icon {
  margin-right: 4px;
}

/* ---- Sticky Footer ---- */
.sticky-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
  z-index: 1000;
}

.action-button {
  width: 100%;
}

.admin-actions {
  display: flex;
  gap: 16rpx;
}

.admin-btn {
  flex: 1;
}

/* ---- Student List Drawer ---- */
.student-list-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.student-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
}

.student-list-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.student-count {
  font-size: 14px;
  color: #666;
}

.student-list-content {
  flex: 1;
  overflow-y: auto;
}

.empty-student-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
}

.empty-student-list t-icon {
  margin-bottom: 12px;
  color: #ccc;
}

.empty-student-list text {
  font-size: 14px;
}

.student-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.student-item:last-child {
  border-bottom: none;
}

.student-info {
  margin-left: 12px;
  flex: 1;
}

.student-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.student-book-time {
  font-size: 12px;
  color: #999;
}
