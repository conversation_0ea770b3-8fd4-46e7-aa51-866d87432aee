/* coach-schedule.wxss */
/*
 * 讲师课表页面样式文件
 *
 * 设计理念：
 * 与 my-bookings 页面保持完全一致的视觉设计风格
 * 1. 使用相同的容器布局和间距设计
 * 2. 采用相同的 TDesign 组件样式覆盖
 * 3. 保持一致的卡片设计和动画效果
 * 4. 统一的颜色主题和字体设计
 * 5. 相同的响应式适配策略
 */

/*
 * 页面根容器样式 - 与 my-bookings 完全一致
 *
 * 布局说明：
 * - display: flex 创建弹性布局容器
 * - flex-direction: column 设置垂直排列
 * - height: 100vh 占满整个视口高度
 * - padding: 12px 设置统一的内边距
 * - background-color: #f5f5f5 设置浅灰色背景
 */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 12px;
  /* padding-bottom: calc(12px + 120rpx + env(safe-area-inset-bottom)); */
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #f5f5f5;
  min-height: 100vh;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-sizing: border-box;
}

/*
 * 筛选栏容器样式 - 保留原有样式以防回退
 *
 * 说明：
 * 这个样式保持与 my-bookings 页面完全一致
 * 为了确保在不同场景下的兼容性
 */
.filter-section {
  width: 100%;
  margin-bottom: 8px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/**
 * 自定义线条选项卡样式 - 与 my-bookings 页面完全一致
 *
 * 功能：覆盖TDesign组件的默认样式
 * 目的：实现优雅的线条选项卡设计
 */
.custom-top-tabs {
  /*
   * 背景 - 完全透明，让容器背景显示
   */
  background-color: transparent;
  border: none;

  /*
   * 移除圆角 - 线条选项卡不需要圆角
   */
  border-radius: 0;

  /*
   * 溢出控制
   */
  overflow: visible;

  /* 字体设置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  font-size: 16px;

  /*
   * 布局控制
   */
  margin: 0;
  width: 100%;
  height: auto;

  /*
   * 最小高度设置为最小值以确保紧凑布局
   */
  min-height: 1px;
}

/**
 * TDesign线条选项卡导航区域样式
 */
.custom-top-tabs .t-tabs__nav {
  /*
   * 内边距 - 优化的内边距
   */
  padding: 0;
  height: auto;
  min-height: 1px;

  /*
   * 移除底部边框，使用父容器的边框
   */
  border-bottom: none;

  /*
   * 布局优化
   */
  display: flex;
  align-items: center;

  /*
   * 背景渐变效果
   */
  background: transparent;
}

/**
 * TDesign线条选项卡项目样式
 */
.custom-top-tabs .t-tabs__item {
  /*
   * 内边距 - 适中的内边距确保点击区域足够大
   */
  padding: 16px 20px !important;

  /*
   * 字体样式
   */
  font-size: 16px !important;
  font-weight: 500 !important;
  color: #666 !important;

  /*
   * 移除背景和边框
   */
  background: transparent !important;
  border: none !important;
  border-radius: 0 !important;

  /*
   * 过渡效果
   */
  transition: color 0.3s ease !important;
}

/**
 * TDesign线条选项卡激活项目样式
 */
.custom-top-tabs .t-tabs__item--active {
  /*
   * 激活状态的字体颜色
   */
  color: #0052d9 !important;
  font-weight: 600 !important;

  /*
   * 移除激活状态的背景
   */
  background: transparent !important;
}

/**
 * TDesign线条选项卡指示器样式
 */
.custom-top-tabs .t-tabs__track {
  /*
   * 指示器颜色
   */
  background-color: #0052d9 !important;

  /*
   * 指示器高度
   */
  height: 3px !important;

  /*
   * 指示器圆角
   */
  border-radius: 2px !important;

  /*
   * 指示器位置
   */
  bottom: 0 !important;
}

/**
 * TDesign线条选项卡内容区域样式
 */
.custom-top-tabs .t-tabs__content {
  /*
   * 移除内容区域的内边距
   */
  padding: 0 !important;

  /*
   * 确保内容区域占满剩余空间
   */
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/**
 * TDesign线条选项卡面板样式
 */
.custom-top-tabs .t-tab-panel {
  /*
   * 面板占满剩余空间
   */
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;

  /*
   * 移除面板的内边距
   */
  padding: 0 !important;
}

/* 预约标签页样式 - 保留备用 */
.booking-tabs {
  width: 100%;
  display: flex;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 4px;
  margin: 0 4px; /* 添加左右边距，避免按钮贴边 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.booking-tab {
  flex: 1;
  text-align: center;
  padding: 8px 12px; /* 增加左右内边距 */
  font-size: 14px;
  border-radius: 6px;
  color: #666;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.booking-tab.active {
  background-color: #0052d9;
  color: white;
  font-weight: 500;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 日期选择器样式 */
.date-section {
  margin-bottom: 12px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.date-tabs-scroll {
  display: flex;
  flex-direction: row;
  white-space: nowrap;
  overflow-x: auto;
  padding: 4px 0 6px 0;
  background: #fff;
  border-radius: 8px;
  margin: 4px 0;
  /* 确保滑动流畅 */
  -webkit-overflow-scrolling: touch;
  flex-shrink: 0; /* 防止日期选择器被压缩 */
}
.date-tab {
  display: inline-block;
  width: 50px;
  text-align: center;
  margin-right: 8px;
  padding: 6px 0 0 0;
  cursor: pointer;
  min-height: 38px;
  /* 防止收缩 */
  flex-shrink: 0;
}
.date-tab:last-child {
  margin-right: 0;
}
.date-tab .tab-label {
  display: block;
  height: 18px;
  line-height: 18px;
}
.date-tab .tab-date {
  display: block;
  font-size: 12px;
  margin-top: 2px;
  height: 16px;
  line-height: 16px;
  color: #bbb;
}

/*
 * 课程列表容器样式 - 与 my-bookings 的 booking-list 保持一致
 *
 * 布局说明：
 * - flex: 1 让列表容器占满剩余空间
 * - display: flex 和 flex-direction: column 创建垂直布局
 * - min-height: 0 确保在flex容器中正确计算高度
 */
.booking-list {
  width: 100%;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/*
 * 课程卡片样式 - 与 my-bookings 的 booking-card 完全一致
 *
 * 设计说明：
 * - 白色背景提供清晰的内容区域
 * - 12px圆角提供现代化的视觉效果
 * - 16px内边距确保内容有足够的呼吸空间
 * - 阴影效果提供层次感和深度
 * - 过渡动画提供流畅的交互体验
 */
.booking-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  position: relative;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  transition: box-shadow 0.3s, background 0.3s, transform 0.3s;
}

/*
 * 滑入动画效果 - 与 my-bookings 完全一致
 *
 * 动画说明：
 * - slide-in 类用于新加载内容的动画效果
 * - 从下方滑入的动画提供优雅的视觉反馈
 */
.booking-card.slide-in {
  animation: slide-in-up 0.6s ease-out;
}

/* 从下方滑入的动画 - 与 my-bookings 完全一致 */
@keyframes slide-in-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
/*
 * 课程头部样式 - 与 my-bookings 保持一致
 */
.course-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

/*
 * 课程标题样式 - 与 my-bookings 完全一致
 *
 * 设计说明：
 * - 18px字体大小确保标题的突出显示
 * - 600字重提供适当的视觉层次
 * - #333颜色确保良好的可读性
 */
.course-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  line-height: 1.4;
  margin-bottom: 8px;
}

/*
 * 预约状态标签样式 - 与 my-bookings 的 booking-status 完全一致
 *
 * 状态颜色设计：
 * - 不同状态使用不同的颜色主题
 * - 背景色和文字色搭配确保良好的对比度
 * - 圆角和内边距提供友好的视觉效果
 */
.booking-status {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  white-space: nowrap;
}

/* 可预约状态 */
.booking-status.available {
  background-color: #e8f5e8;
  color: #52c41a;
}

/* 已预约状态 */
.booking-status.booked {
  background-color: #e6f3ff;
  color: #0052d9;
}

/* 已满员状态 */
.booking-status.full {
  background-color: #fff2e8;
  color: #fa8c16;
}

/* 已结束状态 */
.booking-status.ended {
  background-color: #f0f0f0;
  color: #888;
}

/* 已取消状态 */
.booking-status.cancelled {
  background-color: #fff1f0;
  color: #ff4d4f;
}

/* 已完成状态 */
.booking-status.completed {
  background-color: #f6ffed;
  color: #52c41a;
}

/*
 * 课程信息列表样式 - 与 my-bookings 完全一致
 */
.course-info-list {
  margin-bottom: 12px;
}
.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}
.info-item:last-child {
  margin-bottom: 0;
}
/*
 * 信息项图标样式 - 与 my-bookings 完全一致
 *
 * 设计说明：
 * - margin-right: 8px 确保图标和文字的适当间距
 * - color: #0052d9 使用品牌蓝色突出图标
 * - flex-shrink: 0 防止图标被压缩
 */
.info-item t-icon {
  margin-right: 8px;
  color: #0052d9;
  flex-shrink: 0;
}

/*
 * 信息项文字样式 - 与 my-bookings 完全一致
 */
.info-item text {
  flex: 1;
  word-break: break-all;
}

/* 活动详情单行显示并截断 */
.activity-detail {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}
.course-footer {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-top: 10px;
  border-top: 1px solid #f0f0f0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.tag-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 已预约学员区域样式 */
.booked-students-section {
  margin: 12px 0;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
}

.collapse-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8f8f8;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.collapse-content {
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
}

.student-list {
  padding: 8px 0;
}

.student-item {
  display: flex;
  align-items: center;
  padding: 6px 0;
  font-size: 14px;
  color: #666;
}

.student-item t-icon {
  margin-right: 8px;
  color: #0052d9;
}

.student-name {
  flex: 1;
}

.no-students {
  text-align: center;
  color: #999;
  font-size: 14px;
  padding: 20px 0;
}
#tab-bar-placeholder {
  height: 120rpx;
}
/*
 * 时间轴日期样式 - 与 my-bookings 完全一致
 *
 * 设计说明：
 * - 用于分组显示不同日期的内容
 * - 灰色文字提供层次感
 * - 适当的间距确保视觉分组效果
 */
.timeline-date {
  color: #888;
  font-size: 13px;
  text-align: left;
  margin: 16px 0 8px 8px;
  font-weight: bold;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/*
 * 结束指示器样式 - 与 my-bookings 完全一致
 */
.end-indicator {
  text-align: center;
  color: #bbb;
  font-size: 14px;
  padding: 16px 0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/*
 * 加载指示器样式 - 与 my-bookings 完全一致
 *
 * 设计说明：
 * - 居中显示的加载提示
 * - 使用灰色确保不会过于突出
 * - 适当的内边距确保视觉舒适
 */
.loading-indicator {
  text-align: center;
  color: #888;
  font-size: 15px;
  padding: 16px 0 8px 0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/*
 * 响应式设计 - 与 my-bookings 完全一致
 *
 * 小屏幕适配：
 * - 减小容器内边距
 * - 调整卡片内边距
 * - 优化字体大小
 */
@media (max-width: 375px) {
  .container {
    padding: 8px;
    padding-bottom: env(safe-area-inset-bottom);
  }

  .booking-card {
    padding: 12px;
  }

  .course-title {
    font-size: 17px;
  }

  .info-item {
    font-size: 13px;
  }

  .custom-top-tabs .t-tabs__item {
    padding: 12px 12px !important; /* 进一步减小内边距 */
    font-size: 15px !important;
  }
}