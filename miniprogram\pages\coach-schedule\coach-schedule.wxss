/* coach-schedule.wxss */
/*
 * 讲师课表页面样式文件
 *
 * 设计理念：
 * 完全采用 schedule 页面的视觉设计风格，确保视觉一致性
 * 1. 使用 schedule 页面的渐变背景和布局设计
 * 2. 采用 schedule 页面的 TDesign 组件样式覆盖
 * 3. 保持 schedule 页面的卡片设计和动画效果
 * 4. 统一的颜色主题和字体设计
 * 5. 相同的响应式适配策略
 */

/*
 * 页面根元素样式 - 完全照搬 schedule 页面
 * 设置页面的基础高度和渐变背景
 */
page {
  height: 100%;
  /* 保留渐变背景 */
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  background-attachment: fixed;
}

/*
 * 页面容器样式
 * 确保页面容器占满整个可用高度
 */
.page {
  height: 100%;
  background: transparent;
}

/**
 * .container: 页面根容器样式 - 完全照搬 schedule 页面
 *
 * 设计说明：
 * - 使用较小的内边距(12px)，为列表内容留出更多空间
 * - 底部预留tabBar和安全区域空间
 * - 统一的字体族设置
 * - 固定布局设置，禁止根容器滚动，让顶部区域固定，只有内容区域滚动
 */
.container {
  /*
   * 固定布局设置
   * 禁止根容器滚动，让顶部区域固定，只有内容区域滚动
   */
  height: 100%; /* 占满父容器高度 */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 禁止根容器滚动 */

  /* 内边距：12px固定内边距，为列表内容提供紧凑布局 */
  padding: 12px;

  /*
   * 底部内边距：为tabBar和安全区域预留空间
   * calc(12px + 120rpx + env(safe-area-inset-bottom))
   * - 12px: 与顶部内边距保持一致
   * - 120rpx: tabBar高度
   * - env(safe-area-inset-bottom): 底部安全区域
   */
  padding-bottom: calc(12px + 120rpx + env(safe-area-inset-bottom));

  /*
   * 背景设置：透明背景，让页面级渐变背景显示
   */
  background: transparent;

  /*
   * 字体设置：统一的字体族
   */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;

  /*
   * 盒模型：边框盒模型，确保内边距不会增加容器尺寸
   */
  box-sizing: border-box;
}

/**
 * 视图切换区域样式 - 完全照搬 schedule 页面
 *
 * 功能：包含顶部选项卡的容器区域
 * 设计：为顶部选项卡提供合适的布局空间
 */
.view-section {
  /*
   * 布局控制
   */
  width: 100%;
  flex-shrink: 0; /* 防止在flex容器中被压缩 */

  /*
   * 间距控制
   */
  margin-bottom: 12px;

  /*
   * 字体设置
   */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/**
 * 顶部选项卡区域样式 - 完全照搬 schedule 页面
 *
 * 功能：包含主选项卡组件的容器
 * 布局：优雅的线条式设计，增强视觉层次感
 * 设计理念：从简单的booking-tabs升级为专业的TDesign选项卡
 */
.top-tabs-section {
  /*
   * 布局和定位
   */
  position: relative;

  /*
   * 背景设计 - 渐变背景增加层次感
   */
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);

  /*
   * 边框设计 - 精致的边框效果
   */
  border: 1px solid #f0f0f0;
  border-radius: 8px;

  /*
   * 阴影效果 - 轻微的阴影增加浮起感
   */
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.02),
    0 0 0 1px rgba(0, 0, 0, 0.01);

  /*
   * 内边距 - 适当的内边距
   * 增加4px底部内边距，为激活指示器预留空间
   */
  padding: 0 16px 4px;

  /*
   * 过渡动画
   */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /*
   * 底部装饰线 - 与内容区域的分隔
   */
  border-bottom: 1px solid #e7e7e7;

  /*
   * 层级控制
   */
  z-index: 10;
}

/**
 * 自定义线条选项卡样式 - 完全照搬 schedule 页面
 *
 * 功能：覆盖TDesign组件的默认样式
 * 目的：实现优雅的线条选项卡设计
 */
.custom-top-tabs {
  /*
   * 背景 - 完全透明，让容器背景显示
   */
  background-color: transparent;
  border: none;

  /*
   * 移除圆角 - 线条选项卡不需要圆角
   */
  border-radius: 0;

  /*
   * 溢出控制
   */
  overflow: visible;

  /* 字体设置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  font-size: 16px;

  /*
   * 布局控制
   */
  margin: 0;
  width: 100%;
  height: auto;

  /*
   * 最小高度设置为最小值以确保紧凑布局
   */
  min-height: 1px;
}

/**
 * TDesign线条选项卡导航区域样式 - 完全照搬 schedule 页面
 */
.custom-top-tabs .t-tabs__nav {
  /*
   * 内边距 - 优化的内边距
   */
  padding: 0;
  height: auto;
  min-height: 1px;

  /*
   * 移除底部边框，使用父容器的边框
   */
  border-bottom: none;

  /*
   * 布局优化
   */
  display: flex;
  align-items: center;

  /*
   * 背景渐变效果
   */
  background: transparent;
}

/**
 * TDesign线条选项卡项目样式 - 完全照搬 schedule 页面
 */
.custom-top-tabs .t-tabs__item {
  /*
   * 内边距 - 适中的内边距确保点击区域足够大
   */
  padding: 16px 20px !important;

  /*
   * 字体样式
   */
  font-size: 16px !important;
  font-weight: 500 !important;
  color: #666 !important;

  /*
   * 移除背景和边框
   */
  background: transparent !important;
  border: none !important;
  border-radius: 0 !important;

  /*
   * 底部边框 - 默认透明
   */
  border-bottom: 2px solid transparent !important;

  /*
   * 过渡效果
   */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;

  /*
   * 相对定位 - 为装饰元素提供定位基准
   */
  position: relative;

  /*
   * 最小高度
   */
  min-height: 48px;
}

/*
 * 选项卡项目的装饰效果 - 完全照搬 schedule 页面
 */
.custom-top-tabs .t-tabs__item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #0052d9, transparent);
  transform: translateX(-50%);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/**
 * 激活状态的线条选项卡 - 完全照搬 schedule 页面
 */
.custom-top-tabs .t-tabs__item--active {
  /*
   * 文字颜色和字重
   */
  color: #0052d9 !important;
  font-weight: 600 !important;

  /*
   * 底部蓝色指示线 - 更粗更明显
   */
  border-bottom-color: #0052d9 !important;

  /*
   * 保持透明背景
   */
  background: transparent !important;

  /*
   * 文字阴影效果 - 增加层次感
   */
  text-shadow: 0 0 1px rgba(0, 82, 217, 0.1);
}

/*
 * 激活状态的顶部装饰线 - 完全照搬 schedule 页面
 */
.custom-top-tabs .t-tabs__item--active::before {
  width: 60%; /* 激活时显示顶部装饰线 */
}

/**
 * 非激活状态的选项卡悬停效果 - 完全照搬 schedule 页面
 */
.custom-top-tabs .t-tabs__item:not(.t-tabs__item--active):hover {
  /*
   * 悬停时的文字颜色
   */
  color: #333333 !important;

  /*
   * 保持透明背景
   */
  background: transparent !important;

  /*
   * 悬停时的底部边框效果
   */
  border-bottom-color: rgba(0, 82, 217, 0.3) !important;

  /*
   * 轻微的文字阴影
   */
  text-shadow: 0 0 1px rgba(51, 51, 51, 0.1);
}

/**
 * 日期筛选区域样式 - 采用 schedule 页面的搜索操作区域设计风格
 */
.date-filter-section {
  /*
   * 布局控制
   */
  width: 100%;
  flex-shrink: 0; /* 防止在flex容器中被压缩 */

  /*
   * 间距控制
   */
  margin-bottom: 12px;

  /*
   * 字体设置
   */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/**
 * 日期筛选器容器样式 - 与 schedule 页面的搜索操作区域保持一致
 */
.date-filter-container {
  width: 100%;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  justify-content: flex-start;
  box-sizing: border-box;
  overflow: visible;
  min-height: 44px; /* 与t-tabs的高度保持一致 */

  /*
   * 背景设计 - 与t-tabs的渐变背景保持一致
   */
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);

  /*
   * 边框设计 - 与t-tabs的精致边框保持一致
   */
  border: 1px solid #f0f0f0;
  border-radius: 8px;

  /*
   * 阴影效果 - 与t-tabs的轻微阴影保持一致
   */
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.02),
    0 0 0 1px rgba(0, 0, 0, 0.01);

  /*
   * 内边距 - 适当的内边距
   */
  padding: 6px 16px;

  /*
   * 过渡动画 - 与t-tabs保持一致
   */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/**
 * 横向滚动的日期选择器样式
 */
.date-tabs-scroll {
  /*
   * 布局设置
   */
  width: 100%;
  height: auto;

  /*
   * 滚动设置
   */
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;

  /*
   * 滚动优化
   */
  -webkit-overflow-scrolling: touch;

  /*
   * 隐藏滚动条
   */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
}

.date-tabs-scroll::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}

/**
 * 日期选项卡容器
 */
.date-tabs-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
}

/**
 * 日期选项卡项目样式 - 卡片式设计
 */
.date-tab {
  /*
   * 布局设置
   */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  /*
   * 尺寸设置
   */
  min-width: 60px;
  height: 56px;
  padding: 6px 8px;

  /*
   * 防止收缩
   */
  flex-shrink: 0;

  /*
   * 背景和边框
   */
  background: #ffffff;
  border: 1px solid #e7e7e7;
  border-radius: 8px;

  /*
   * 阴影效果
   */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);

  /*
   * 过渡动画
   */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /*
   * 交互设置
   */
  cursor: pointer;

  /*
   * 相对定位 - 为指示器提供定位基准
   */
  position: relative;

  /*
   * 字体设置
   */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/**
 * 日期选项卡悬停效果
 */
.date-tab:hover {
  /*
   * 悬停时的背景色
   */
  background: #f8f9fa;

  /*
   * 悬停时的边框色
   */
  border-color: #d0d7de;

  /*
   * 悬停时的阴影
   */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);

  /*
   * 轻微的向上移动
   */
  transform: translateY(-1px);
}

/**
 * 激活状态的日期选项卡
 */
.date-tab.active {
  /*
   * 激活状态的背景色
   */
  background: linear-gradient(135deg, #0052d9 0%, #0066ff 100%);

  /*
   * 激活状态的边框色
   */
  border-color: #0052d9;

  /*
   * 激活状态的阴影
   */
  box-shadow:
    0 2px 8px rgba(0, 82, 217, 0.2),
    0 0 0 1px rgba(0, 82, 217, 0.1);

  /*
   * 激活状态的变换
   */
  transform: translateY(-1px);
}

/**
 * 激活状态的日期选项卡文字颜色
 */
.date-tab.active .date-tab-label,
.date-tab.active .date-tab-date {
  color: #ffffff !important;
  font-weight: 600;
}

/**
 * 日期选项卡标签文字样式
 */
.date-tab-label {
  /*
   * 字体设置
   */
  font-size: 13px;
  font-weight: 500;
  color: #333333;

  /*
   * 布局设置
   */
  line-height: 1.2;
  text-align: center;

  /*
   * 间距设置
   */
  margin-bottom: 2px;

  /*
   * 过渡动画
   */
  transition: color 0.3s ease;
}

/**
 * 日期选项卡日期文字样式
 */
.date-tab-date {
  /*
   * 字体设置
   */
  font-size: 11px;
  font-weight: 400;
  color: #666666;

  /*
   * 布局设置
   */
  line-height: 1.2;
  text-align: center;

  /*
   * 过渡动画
   */
  transition: color 0.3s ease;
}

/**
 * 日期选项卡选中状态指示器
 */
.date-tab-indicator {
  /*
   * 定位设置
   */
  position: absolute;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);

  /*
   * 尺寸设置
   */
  width: 20px;
  height: 3px;

  /*
   * 样式设置
   */
  background: #0052d9;
  border-radius: 2px;

  /*
   * 动画效果
   */
  animation: indicator-pulse 1.5s ease-in-out infinite;
}

/**
 * 指示器脉冲动画
 */
@keyframes indicator-pulse {
  0%, 100% {
    opacity: 1;
    transform: translateX(-50%) scale(1);
  }
  50% {
    opacity: 0.7;
    transform: translateX(-50%) scale(1.1);
  }
}

/*
 * 注意：自定义线条选项卡样式已在上方定义，这里不重复
 * 保持 schedule 页面的设计风格
 */

/*
 * 注意：TDesign组件的详细样式已在上方定义
 * 这里保持简洁，避免重复定义
 */

/*
 * 注意：日期选择器样式已在上方重新定义为 schedule 页面风格
 * 这里删除重复的旧样式定义
 */

/*
 * 课程列表容器样式 - 与 my-bookings 的 booking-list 保持一致
 *
 * 布局说明：
 * - flex: 1 让列表容器占满剩余空间
 * - display: flex 和 flex-direction: column 创建垂直布局
 * - min-height: 0 确保在flex容器中正确计算高度
 */
.booking-list {
  width: 100%;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/*
 * 课程卡片样式 - 与 my-bookings 的 booking-card 完全一致
 *
 * 设计说明：
 * - 白色背景提供清晰的内容区域
 * - 12px圆角提供现代化的视觉效果
 * - 16px内边距确保内容有足够的呼吸空间
 * - 阴影效果提供层次感和深度
 * - 过渡动画提供流畅的交互体验
 */
.booking-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  position: relative;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  transition: box-shadow 0.3s, background 0.3s, transform 0.3s;
}

/*
 * 滑入动画效果 - 与 my-bookings 完全一致
 *
 * 动画说明：
 * - slide-in 类用于新加载内容的动画效果
 * - 从下方滑入的动画提供优雅的视觉反馈
 */
.booking-card.slide-in {
  animation: slide-in-up 0.6s ease-out;
}

/* 从下方滑入的动画 - 与 my-bookings 完全一致 */
@keyframes slide-in-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
/*
 * 课程头部样式 - 与 my-bookings 保持一致
 */
.course-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

/*
 * 课程标题样式 - 与 my-bookings 完全一致
 *
 * 设计说明：
 * - 18px字体大小确保标题的突出显示
 * - 600字重提供适当的视觉层次
 * - #333颜色确保良好的可读性
 */
.course-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  line-height: 1.4;
  margin-bottom: 8px;
}

/*
 * 预约状态标签样式 - 与 my-bookings 的 booking-status 完全一致
 *
 * 状态颜色设计：
 * - 不同状态使用不同的颜色主题
 * - 背景色和文字色搭配确保良好的对比度
 * - 圆角和内边距提供友好的视觉效果
 */
.booking-status {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  white-space: nowrap;
}

/* 可预约状态 */
.booking-status.available {
  background-color: #e8f5e8;
  color: #52c41a;
}

/* 已预约状态 */
.booking-status.booked {
  background-color: #e6f3ff;
  color: #0052d9;
}

/* 已满员状态 */
.booking-status.full {
  background-color: #fff2e8;
  color: #fa8c16;
}

/* 已结束状态 */
.booking-status.ended {
  background-color: #f0f0f0;
  color: #888;
}

/* 已取消状态 */
.booking-status.cancelled {
  background-color: #fff1f0;
  color: #ff4d4f;
}

/* 已完成状态 */
.booking-status.completed {
  background-color: #f6ffed;
  color: #52c41a;
}

/*
 * 课程信息列表样式 - 与 my-bookings 完全一致
 */
.course-info-list {
  margin-bottom: 12px;
}
.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}
.info-item:last-child {
  margin-bottom: 0;
}
/*
 * 信息项图标样式 - 与 my-bookings 完全一致
 *
 * 设计说明：
 * - margin-right: 8px 确保图标和文字的适当间距
 * - color: #0052d9 使用品牌蓝色突出图标
 * - flex-shrink: 0 防止图标被压缩
 */
.info-item t-icon {
  margin-right: 8px;
  color: #0052d9;
  flex-shrink: 0;
}

/*
 * 信息项文字样式 - 与 my-bookings 完全一致
 */
.info-item text {
  flex: 1;
  word-break: break-all;
}

/* 活动详情单行显示并截断 */
.activity-detail {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}
.course-footer {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-top: 10px;
  border-top: 1px solid #f0f0f0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.tag-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 已预约学员区域样式 */
.booked-students-section {
  margin: 12px 0;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
}

.collapse-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8f8f8;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.collapse-content {
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
}

.student-list {
  padding: 8px 0;
}

.student-item {
  display: flex;
  align-items: center;
  padding: 6px 0;
  font-size: 14px;
  color: #666;
}

.student-item t-icon {
  margin-right: 8px;
  color: #0052d9;
}

.student-name {
  flex: 1;
}

.no-students {
  text-align: center;
  color: #999;
  font-size: 14px;
  padding: 20px 0;
}
#tab-bar-placeholder {
  height: 120rpx;
}
/*
 * 时间轴日期样式 - 完全照搬 schedule 页面的设计风格
 *
 * 设计说明：
 * - 用于分组显示不同日期的内容
 * - 采用 schedule 页面的时间轴设计风格
 * - 优雅的视觉层次和间距关系
 * - 与内容卡片的完美配合
 */
.timeline-date {
  /*
   * 字体设置 - 与 schedule 页面保持一致
   */
  font-size: 14px;
  font-weight: 600;
  color: #333333;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;

  /*
   * 布局设置
   */
  text-align: left;
  line-height: 1.4;

  /*
   * 间距设置 - 与 schedule 页面的时间轴间距保持一致
   */
  margin: 24px 0 12px 0;
  padding: 8px 16px;

  /*
   * 背景和边框 - 增加视觉层次感
   */
  background: linear-gradient(90deg, #f8f9fa 0%, #ffffff 100%);
  border-left: 4px solid #0052d9;
  border-radius: 0 8px 8px 0;

  /*
   * 阴影效果 - 轻微的阴影增加浮起感
   */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  /*
   * 相对定位 - 为装饰元素提供定位基准
   */
  position: relative;

  /*
   * 过渡动画
   */
  transition: all 0.3s ease;
}

/**
 * 时间轴日期的装饰元素
 */
.timeline-date::before {
  content: '';
  position: absolute;
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  background: #0052d9;
  border-radius: 50%;
  box-shadow: 0 0 0 3px #ffffff, 0 0 0 5px rgba(0, 82, 217, 0.2);
}

/**
 * 时间轴日期悬停效果
 */
.timeline-date:hover {
  /*
   * 悬停时的背景渐变
   */
  background: linear-gradient(90deg, #f0f8ff 0%, #ffffff 100%);

  /*
   * 悬停时的阴影增强
   */
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);

  /*
   * 悬停时的左边框颜色变化
   */
  border-left-color: #0066ff;
}

/*
 * 结束指示器样式 - 与 my-bookings 完全一致
 */
.end-indicator {
  text-align: center;
  color: #bbb;
  font-size: 14px;
  padding: 16px 0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/*
 * 加载指示器样式 - 与 my-bookings 完全一致
 *
 * 设计说明：
 * - 居中显示的加载提示
 * - 使用灰色确保不会过于突出
 * - 适当的内边距确保视觉舒适
 */
.loading-indicator {
  text-align: center;
  color: #888;
  font-size: 15px;
  padding: 16px 0 8px 0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/**
 * 响应式设计 - 完全照搬 schedule 页面的响应式策略
 *
 * 设计理念：
 * 确保在不同屏幕尺寸下都能提供优秀的用户体验
 * 重点优化小屏幕设备的显示效果
 */

/**
 * 小屏幕适配 (≤ 375px) - 与 schedule 页面完全一致
 */
@media (max-width: 375px) {
  /*
   * 容器适配
   */
  .container {
    padding: 8px;
    padding-bottom: calc(8px + 120rpx + env(safe-area-inset-bottom));
  }

  /*
   * 顶部选项卡区域适配
   */
  .top-tabs-section {
    padding: 0 12px 4px;
    margin-bottom: 8px;
  }

  /*
   * 选项卡项目适配
   */
  .custom-top-tabs .t-tabs__item {
    padding: 12px 16px !important;
    font-size: 15px !important;
  }

  /*
   * 日期筛选器适配
   */
  .date-filter-container {
    padding: 4px 12px;
  }

  .date-tab {
    min-width: 56px;
    height: 52px;
    padding: 4px 6px;
  }

  .date-tab-label {
    font-size: 12px;
  }

  .date-tab-date {
    font-size: 10px;
  }

  /*
   * 课程卡片适配
   */
  .booking-card {
    padding: 12px;
    margin-bottom: 12px;
  }

  .course-title {
    font-size: 17px;
  }

  .info-item {
    font-size: 13px;
  }

  /*
   * 时间轴适配
   */
  .timeline-date {
    font-size: 13px;
    margin: 20px 0 10px 0;
    padding: 6px 12px;
  }
}

/**
 * 中等屏幕适配 (376px - 414px) - 与 schedule 页面完全一致
 */
@media (min-width: 376px) and (max-width: 414px) {
  /*
   * 容器适配
   */
  .container {
    padding: 10px;
  }

  /*
   * 选项卡项目适配
   */
  .custom-top-tabs .t-tabs__item {
    padding: 14px 18px !important;
  }

  /*
   * 日期选项卡适配
   */
  .date-tab {
    min-width: 58px;
    height: 54px;
  }
}

/**
 * 大屏幕优化 (≥ 415px) - 与 schedule 页面完全一致
 */
@media (min-width: 415px) {
  /*
   * 容器适配
   */
  .container {
    padding: 16px;
    max-width: 800px;
    margin: 0 auto;
  }

  /*
   * 选项卡项目适配
   */
  .custom-top-tabs .t-tabs__item {
    padding: 18px 24px !important;
  }

  /*
   * 课程卡片适配
   */
  .booking-card {
    padding: 20px;
  }

  /*
   * 时间轴适配
   */
  .timeline-date {
    font-size: 15px;
    padding: 10px 20px;
  }
}