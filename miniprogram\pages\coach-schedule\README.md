# 讲师课表页面重构说明

## 重构概述

本次重构将 `coach-schedule` 页面的架构和实现方式与 `my-bookings` 页面保持完全一致，同时保留所有原有功能并更新相关术语。

## 重构内容

### 1. 页面配置文件 (coach-schedule.json)
- ✅ 添加了与 my-bookings 相同的 TDesign 组件配置
- ✅ 统一了页面设置（禁用下拉刷新、设置背景文字样式）
- ✅ 确保组件版本和配置的一致性

### 2. 页面结构文件 (coach-schedule.wxml)
- ✅ 采用 TDesign 的 `t-tabs` 组件替代原有的自定义Tab
- ✅ 使用 `t-empty` 组件显示空状态
- ✅ 统一了 `t-icon` 组件的使用方式
- ✅ 保持了原有的日期选择器功能
- ✅ 统一了卡片结构和信息展示方式
- ✅ 保留了学员管理的折叠面板功能

### 3. 页面样式文件 (coach-schedule.wxss)
- ✅ 采用与 my-bookings 完全一致的容器布局
- ✅ 添加了 TDesign 组件的样式覆盖
- ✅ 统一了卡片设计和动画效果
- ✅ 保持了相同的颜色主题和字体设计
- ✅ 实现了相同的响应式适配策略

### 4. 页面逻辑文件 (coach-schedule.js)
- ✅ 采用与 my-bookings 相同的数据管理模式
- ✅ 实现了多层数据结构：原始数据 → 筛选数据 → 显示数据
- ✅ 统一了分页加载逻辑和状态管理
- ✅ 保持了原有的日期筛选功能
- ✅ 更新了术语：教练→讲师，会员→学员
- ✅ 保留了所有原有业务功能

## 术语更新对照表

| 原术语 | 新术语 | 说明 |
|--------|--------|------|
| 教练 | 讲师 | 课程授课人员 |
| 会员 | 学员 | 参与活动的人员 |
| 健身房 | 活动室 | 活动场所 |
| 会员卡 | 考勤卡 | 身份识别卡片 |

## 功能验证清单

### 基础功能
- [ ] 页面正常加载和显示
- [ ] Tab切换功能正常
- [ ] 数据加载和显示正确

### 当前课程功能
- [ ] 当前课程列表正常显示
- [ ] 日期筛选器功能正常
- [ ] 分页加载功能正常
- [ ] 课程卡片信息显示正确

### 历史课程功能
- [ ] 历史课程列表正常显示
- [ ] 时间轴分组显示正确
- [ ] 分页加载功能正常
- [ ] 下拉刷新功能正常

### 交互功能
- [ ] 课程卡片点击跳转正常
- [ ] 学员列表折叠展开正常
- [ ] 加载状态提示正确
- [ ] 错误处理和提示正常

### 视觉效果
- [ ] 样式与 my-bookings 保持一致
- [ ] 动画效果流畅
- [ ] 响应式适配正常
- [ ] 图标和颜色主题统一

## 注意事项

1. **数据兼容性**：确保云函数返回的数据格式与新的数据处理逻辑兼容
2. **状态管理**：验证Tab切换时的状态保持和数据更新
3. **性能优化**：确认分页加载和数据缓存策略正常工作
4. **用户体验**：验证加载提示、错误处理和交互反馈

## 测试建议

1. **功能测试**：逐一验证上述功能清单中的每一项
2. **兼容性测试**：在不同设备和微信版本上测试
3. **性能测试**：测试大量数据下的页面性能
4. **用户体验测试**：确保与 my-bookings 页面的一致性

## 后续优化

1. 根据测试结果调整细节实现
2. 优化数据加载和缓存策略
3. 完善错误处理和用户提示
4. 持续保持与 my-bookings 的架构一致性
