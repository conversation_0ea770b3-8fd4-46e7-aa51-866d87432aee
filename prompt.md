# 活动室预约小程序需求文档（微信小程序）

> 说明：本需求文档基于当前 miniprogram/pages 目录下的页面结构，所有页面、功能、数据模型均与实际小程序保持一致。页面命名、入口、功能描述均以实际开发为准。

## 一、项目简介

本项目为活动室预约管理微信小程序，基于微信云开发，服务于学员、讲师、管理员三类用户。支持活动预约、活动管理、用户与角色管理、会员卡管理等核心功能。所有功能均以易用性和高效性为核心，界面风格统一，交互友好。

## 二、页面结构与功能说明

### 1. 首页（index）
- **功能**：展示品牌形象、门店信息、实景照片、快捷入口。
- **主要内容**：
  - 顶部背景图+Logo+活动室名称
  - 联系电话、门店地址、公告信息（如有）
  - 实景照片展示区（3张）
  - 快捷操作按钮：“约活动”（跳转活动表）、“课程卡”（跳转会员卡）
  - 底部固定TabBar（首页/活动表/我的）
- **交互**：所有按钮、图片均有点击反馈，信息区块风格统一。

### 2. 活动表页面（schedule）
- **功能**：学员浏览、预约、取消预约活动，讲师/管理员查看活动及预约情况。
- **主要内容**：
  - 顶部筛选栏（日期、讲师昵称）
  - “全部活动”视图（日历分组，展示status为online的活动）
  - “历史活动”视图（已结束活动列表）
  - 活动卡片（活动名称、时间、讲师昵称、剩余名额、预约状态）
  - 预约/取消预约按钮（学员专有，操作前弹窗确认，操作后Toast反馈）
  - 下拉刷新、分页加载
  - 底部TabBar
- **交互**：所有操作均有TDesign Toast反馈，风格统一。

### 3. 我的页面（profile）
- **功能**：用户登录、个人信息展示、分角色功能入口。
- **主要内容**：
  - 未登录：Logo、欢迎语、微信一键登录按钮、用户协议勾选
  - 已登录：头像、昵称、角色标签、功能分区（个人中心/学员功能/讲师功能/管理功能）
  - 个人中心：个人资料、关于
  - 学员功能：我的活动、会员卡
  - 讲师功能：我的活动表
  - 管理员功能：用户管理、活动管理、会员卡管理、系统设置
  - 退出登录按钮、底部TabBar
- **交互**：分区、按钮根据用户角色动态显示，所有操作有反馈。

### 4. 个人资料编辑（profile-edit）
- **功能**：用户编辑头像、昵称、性别等基础信息。
- **主要内容**：
  - 头像、昵称、性别、注册时间、最近登录时间、角色等
  - 编辑按钮、保存/取消操作
- **交互**：所有修改有二次确认，操作结果Toast提示。

### 5. 讲师活动表（coach-schedule）
- **功能**：讲师查看自己主讲的已上线活动及预约学员名单。
- **主要内容**：
  - 活动卡片（活动名称、时间、地点、最大/已预约人数、可预约标签）
  - 预约学员名单（学员昵称、头像）
  - 下拉刷新、分页加载、空状态
  - 底部TabBar
- **交互**：仅展示“已上线活动”，无活动维护权限。

### 6. 活动管理（course-management）
- **功能**：管理员新增、编辑、删除、上下线活动，管理活动模板。
- **主要内容**：
  - 顶部Tabs（活动维护/模板维护）
  - 活动维护：添加活动按钮、筛选Tabs（全部/已上线/未上线/历史活动）、活动卡片（含编辑/删除/上下线操作）
  - 模板维护：模板卡片（编辑/删除/应用为新活动）
  - 下拉刷新、分页加载、底部TabBar
- **交互**：所有操作有确认弹窗和Toast反馈。

### 7. 活动编辑（course-edit）
- **功能**：新建/编辑活动，保存为模板。
- **主要内容**：
  - 表单区：活动名称、时间、地点、讲师（多选）、人数、难度、描述、适合人群、状态等
  - 操作按钮：保存、保存为模板、取消
- **交互**：必填项校验，所有操作有确认弹窗和Toast反馈。

### 8. 活动详情（course-detail）
- **功能**：展示活动详细信息，学员预约/取消预约，讲师/管理员查看预约名单，管理员编辑/删除活动。
- **主要内容**：
  - 活动名称、类型标签、活动ID（可复制）
  - 活动时间、地点、讲师、难度、适合人群、描述、剩余名额
  - 预约/取消预约按钮（学员）、预约学员名单（讲师/管理员）、编辑/删除按钮（管理员）
- **交互**：所有操作有确认弹窗和Toast反馈。

### 9. 会员卡（membership-card）
- **功能**：学员查看本人会员卡信息。
- **主要内容**：
  - 会员卡卡片（编号、有效期、总次数、剩余次数、颁发日期、状态）
  - 空状态提示、下拉刷新、底部TabBar
- **交互**：所有操作有Toast反馈。

### 10. 会员卡管理（membership-card-management）
- **功能**：管理员新建、颁发、吊销、延期、冻结会员卡，查看会员卡列表。
- **主要内容**：
  - 顶部Tabs（全部/已颁发/未颁发）
  - 新建按钮、会员卡卡片（含操作按钮）
  - 空状态、下拉刷新、分页加载、底部TabBar
- **交互**：所有操作有确认弹窗和Toast反馈。

### 11. 我的预约（my-bookings）
- **功能**：学员查看、取消本人已预约活动，查看历史活动。
- **主要内容**：
  - 已预约活动列表（可取消）、历史活动列表
  - 活动卡片（活动名称、时间、讲师、状态等）
- **交互**：取消预约有确认弹窗和Toast反馈。

### 12. 用户管理（user-management）
- **功能**：管理员查看、搜索、筛选、修改用户角色，禁用/启用账号。
- **主要内容**：
  - 顶部Tabs（全部用户/学员/讲师/管理员）
  - 搜索栏、用户卡片（头像、昵称、openid、角色、注册/登录时间、操作按钮）
  - 空状态、下拉刷新、分页加载、底部TabBar
- **交互**：所有操作有确认弹窗和Toast反馈。

### 13. 系统设置（system-settings）
- **功能**：管理员设置预约取消时间、维护模式、联系电话、门店地址、公告信息。
- **主要内容**：
  - 预约取消时间（输入框/滑块）、维护模式开关、联系电话、门店地址、公告信息输入框
  - 保存按钮、下拉刷新、底部TabBar
- **交互**：所有设置项修改有二次确认和Toast反馈。

### 14. 相册管理（album-management）
- **功能**：管理活动室实景照片、宣传图片等（如有）。
- **主要内容**：
  - 照片列表、上传/删除按钮
- **交互**：所有操作有Toast反馈。

### 15. 讲师管理（coaches）
- **功能**：管理员管理讲师信息。
- **主要内容**：
  - 讲师列表、添加/编辑/删除讲师按钮
- **交互**：所有操作有确认弹窗和Toast反馈。

## 三、数据模型（微信云开发数据库）

### 1. 用户（users）
- _id: string
- openid: string
- nickName: string
- avatarUrl: string
- gender: number
- language: string
- roles: array<string>
- createTime: date
- updateTime: date
- lastLoginTime: date

### 2. 讲师（coachInfo）
- _id: string
- openid: string
- introduction: string
- specialties: string
- createTime: date
- updateTime: date

### 3. 活动模板（coursesTemplate）
- _id: string
- name: string
- type: string
- coach: array<string>（讲师openid数组）
- venue: string
- capacity: number
- difficulty: string
- description: string
- suitableFor: string

### 4. 活动（courses）
- _id: string
- name: string
- type: string
- coach: array<string>（讲师openid数组）
- coachName: array<string>（讲师名字）
- startTime: date
- endTime: date
- venue: string
- capacity: number
- difficulty: string
- description: string
- suitableFor: string
- status: string
- createTime: date
- updateTime: date

### 5. 预约记录（bookings）
- _id: string
- userId: string
- courseId: string
- courseName: string
- cardNumber: string
- createTime: date
- updateTime: date
- status: string

### 6. 会员卡（membershipCard）
- _id: string
- userId: string
- cardNumber: string
- validFrom: date
- validTo: date
- issueDate: date
- totalTimes: number
- remainingTimes: number
- status: string

### 7. 系统设置（systemSettings）
- cancelBookingHours: number
- maintenanceMode: boolean
- contactPhone: string
- storeAddress: string
- announcement: string

## 四、全局交互与风格规范
- 所有页面底部均为统一风格TabBar（首页/活动表/我的）
- 操作提示、弹窗、Toast均统一使用TDesign组件，风格一致
- 文字、标签、间距、按钮等界面元素风格统一，符合微信小程序与TDesign设计规范
- 数据变动自动同步，支持下拉刷新、分页加载
- 所有操作均有明确反馈，重要操作需二次确认

---

> 本文档为当前小程序实际页面与功能的权威说明，后续如有页面结构或功能调整，请同步更新本文件。
