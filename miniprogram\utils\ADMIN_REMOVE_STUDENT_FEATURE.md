# 管理员移除学员预约功能说明

## 功能概述

在course-management页面新增了管理员为学员取消预约的功能。该功能允许管理员在任何时候为学员取消预约，不受时间限制，即使活动已经结束也可以操作。

## 功能特点

### 1. 权限控制
- **仅管理员可用**：只有具有"管理员"角色的用户才能看到和使用此功能
- **前端权限控制**：通过 `wx:if="{{isAdmin}}"` 控制按钮显示
- **后端权限验证**：云函数中验证操作者的管理员权限

### 2. 无时间限制
- **突破常规限制**：不受课程开始时间、结束时间等限制
- **历史活动支持**：即使活动已结束，仍可为学员取消预约
- **灵活处理**：适用于各种特殊情况的处理

### 3. 安全机制
- **5秒倒计时确认**：防止误操作，强制管理员思考操作后果
- **详细确认信息**：清晰显示要移除的学员信息和操作说明
- **操作记录**：在数据库中记录取消原因和操作者信息

### 4. 自动退还
- **考勤卡次数退还**：自动退还学员的考勤卡次数
- **事务处理**：确保预约取消和次数退还的原子性操作
- **数据一致性**：避免数据不一致的问题

## UI设计

### 1. 移除图标位置
- **位置**：在已预约学员折叠区域中，每个学员名字后面
- **样式**：小尺寸的删除图标，红色主题
- **对齐**：所有移除图标靠右对齐，上下对齐
- **交互**：点击区域有padding增强，悬停有背景色反馈

### 2. 确认对话框
- **标题**：移除学员预约
- **内容**：包含学员姓名、操作说明、倒计时提示
- **按钮**：带倒计时的确认按钮和取消按钮

## 技术实现

### 1. 前端实现 (`course-management.js`)

#### 数据结构
```javascript
// 移除学员对话框相关数据
removeStudentDialogVisible: false,        // 对话框显示状态
removeStudentDialogContent: '',           // 对话框内容
removeStudentDialogConfirmBtn: '确认移除(5)', // 确认按钮文字
removeStudentCountdown: 5,                // 倒计时秒数
removeStudentCountdownTimer: null,        // 倒计时定时器
removeStudentData: null,                  // 待移除的学员数据
```

#### 主要方法
- `onRemoveStudent()`: 处理移除按钮点击事件
- `_showRemoveStudentDialog()`: 显示确认对话框
- `_startRemoveStudentCountdown()`: 启动倒计时
- `_clearRemoveStudentCountdown()`: 清除倒计时定时器
- `onRemoveStudentConfirm()`: 确认移除操作
- `onRemoveStudentCancel()`: 取消移除操作

### 2. 后端实现 (`bookingManagement/index.js`)

#### 云函数action
```javascript
case 'adminCancelBooking':
  return await adminCancelBooking(data);
```

#### 主要功能
- **权限验证**：验证操作者是否为管理员
- **数据验证**：验证预约记录的有效性
- **事务处理**：原子性地更新预约状态和退还次数
- **操作记录**：记录取消原因和操作者信息

### 3. UI样式 (`course-management.wxss`)

```css
.student-item {
  justify-content: space-between; /* 确保移除图标靠右对齐 */
}

.student-name {
  margin-right: 8px; /* 为移除图标留出空间 */
}

.remove-student-icon {
  flex-shrink: 0; /* 防止图标被压缩 */
  margin-left: auto; /* 确保图标靠右对齐 */
  padding: 4px; /* 增加点击区域 */
  cursor: pointer; /* 鼠标悬停时显示手型 */
  border-radius: 4px; /* 圆角效果 */
  transition: background-color 0.2s; /* 平滑的背景色过渡 */
}

.remove-student-icon:hover {
  background-color: rgba(227, 77, 89, 0.1); /* 浅红色背景 */
}
```

## 使用流程

### 1. 查看学员列表
1. 进入course-management页面
2. 找到要操作的课程
3. 点击"已预约学员"区域展开学员列表

### 2. 移除学员预约
1. 在学员列表中找到要移除的学员
2. 点击学员名字后面的"移除"按钮
3. 在确认对话框中等待5秒倒计时
4. 倒计时结束后点击"确认移除"按钮

### 3. 操作结果
- 成功：显示"已为学员XXX取消预约"提示
- 失败：显示具体的错误信息
- 页面自动刷新，显示最新的预约状态

## 数据库变更

### 1. 预约记录更新
```javascript
{
  status: 'cancelled',           // 状态改为已取消
  cancelTime: new Date(),        // 取消时间
  cancelReason: '管理员取消',     // 取消原因
  cancelBy: 'admin',             // 取消方式
  adminOpenid: OPENID            // 操作管理员的openid
}
```

### 2. 考勤卡次数退还
```javascript
{
  remainingTimes: db.command.inc(1) // 剩余次数增加1
}
```

## 测试场景

### 1. 权限测试
- **管理员账号**：应该能看到移除按钮并成功操作
- **非管理员账号**：不应该看到移除按钮
- **未登录用户**：不应该看到移除按钮

### 2. 功能测试
- **正常预约**：能够成功移除学员预约
- **已取消预约**：不能重复取消
- **不存在的预约**：显示相应错误信息

### 3. 时间限制测试
- **未开始的活动**：可以移除预约
- **进行中的活动**：可以移除预约
- **已结束的活动**：可以移除预约

### 4. 倒计时测试
- **倒计时期间**：确认按钮不可用
- **倒计时结束**：确认按钮可用
- **取消操作**：倒计时正确重置

## 注意事项

### 1. 使用频率
- 这个功能设计为低频使用
- 主要用于处理特殊情况和紧急情况
- 不建议作为常规的预约管理方式

### 2. 操作记录
- 所有操作都会在数据库中留下记录
- 包括操作时间、操作者、取消原因等
- 便于后续的审计和问题追踪

### 3. 用户体验
- 5秒倒计时确保操作的谨慎性
- 清晰的提示信息避免误解
- 自动刷新确保数据的实时性

## 扩展可能

### 1. 批量操作
- 可以扩展为支持批量移除多个学员
- 添加全选/反选功能

### 2. 取消原因
- 可以添加取消原因的选择或输入
- 便于更好的记录和管理

### 3. 通知功能
- 可以添加短信或微信通知功能
- 及时告知学员预约被取消的信息
