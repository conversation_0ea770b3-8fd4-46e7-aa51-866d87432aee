/* profile.wxss */
/*
 * 个人中心页面样式文件
 *
 * 设计理念：
 * 1. 卡片式设计：使用白色卡片承载内容，增强层次感
 * 2. 圆角设计：大量使用圆角元素，营造现代、友好的视觉效果
 * 3. 阴影效果：适度使用阴影增强立体感和层次
 * 4. 色彩搭配：主要使用蓝色系作为主色调，灰色作为辅助色
 * 5. 响应式布局：使用Flexbox确保在不同屏幕尺寸下的良好显示
 * 6. 单位使用：混合使用rpx和px，rpx用于响应式尺寸，px用于固定尺寸
 *
 * 页面结构：
 * - 未登录状态：居中的登录卡片
 * - 已登录状态：用户信息卡片 + 功能菜单列表
 */

/**
 * .container: 页面根容器样式
 *
 * 设计说明：
 * - 使用rpx单位实现响应式布局
 * - 底部padding特殊处理，为tabBar和安全区域预留空间
 * - 统一字体族，确保整个页面字体一致性
 *
 * 单位说明：
 * - rpx: 响应式像素，750rpx = 屏幕宽度，自动适配不同屏幕
 * - px: 固定像素，不会根据屏幕缩放
 * - calc(): CSS计算函数，支持不同单位的混合运算
 * - env(): CSS环境变量函数，获取系统安全区域信息
 */
.container {
  /* 基础内边距：32rpx响应式内边距 */
  padding: 32rpx;

  /*
   * 底部内边距特殊处理：
   * calc(32rpx + 120rpx + env(safe-area-inset-bottom))
   * - 32rpx: 基础内边距
   * - 120rpx: tabBar高度预留
   * - env(safe-area-inset-bottom): iPhone X等全面屏底部安全区域
   *
   * 这样设计确保内容不会被tabBar或安全区域遮挡
   */
  padding-bottom: calc(32rpx + 120rpx + env(safe-area-inset-bottom));

  /* 渐变背景：从浅蓝到浅灰的线性渐变，营造更丰富的视觉层次 */
  background: linear-gradient(135deg, #f0f8ff 0%, #f5f5f5 50%, #fafafa 100%);

  /* 最小高度：确保页面至少占满整个视口 */
  min-height: 100vh;

  /*
   * 字体族设置：
   * 定义了完整的字体回退链，确保在不同系统上都有良好的字体显示
   * - "PingFang SC": 苹果系统简体中文字体（iOS/macOS）
   * - "PingFang": 苹果系统字体的通用名称
   * - "Helvetica Neue": 苹果系统英文字体
   * - "Helvetica": Helvetica字体的通用版本
   * - "Arial": Windows系统常用字体
   * - sans-serif: 无衬线字体族（最终回退）
   */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;

  /* 盒模型：padding和border包含在总宽度内 */
  box-sizing: border-box;
}

/* Logo样式 */
.logo {
  margin-right: 12px;
  border: 3px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.avatar {
  margin-right: 24rpx;
  /* 更厚的白色边框，增强层次感 */
  border: 4rpx solid white;
  /* 多层阴影效果，营造浮起的感觉 */
  box-shadow:
    0 4rpx 12rpx rgba(0, 0, 0, 0.15),
    0 2rpx 6rpx rgba(0, 0, 0, 0.08);
  /* 添加过渡动画 */
  transition: all 0.3s ease;
}

/* 头像悬浮效果（为未来扩展准备） */
.avatar:hover {
  transform: scale(1.05);
  box-shadow:
    0 6rpx 20rpx rgba(0, 0, 0, 0.2),
    0 3rpx 10rpx rgba(0, 0, 0, 0.12);
}

/* 登录区域样式 - 优化未登录状态 */
.login-section {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.login-card {
  /* 渐变背景，从纯白到微蓝的渐变 */
  background: linear-gradient(135deg, #ffffff 0%, #fafbff 100%);
  border-radius: 24rpx; /* 更大的圆角 */
  /* 更强的阴影效果，突出登录卡片的重要性 */
  box-shadow:
    0 8rpx 32rpx rgba(0, 82, 217, 0.08),
    0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  padding: 60rpx 40rpx; /* 增加上下内边距 */
  display: flex;
  justify-content: center;
  /* 微妙的边框，增强卡片质感 */
  border: 1rpx solid rgba(0, 82, 217, 0.05);
  /* 添加动画过渡 */
  transition: all 0.3s ease;
}

.login-content {
  text-align: center;
  width: 100%;
  max-width: 320px;
  padding: 40px 20px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 统一文字样式 */
.login-text {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 24px 0 12px 0;
  line-height: 1.4;
  letter-spacing: 0.5px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.login-tip {
  font-size: 15px;
  color: #666666;
  margin-bottom: 40px;
  line-height: 1.5;
  font-weight: 400;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 登录按钮区域样式 */
.login-button-section {
  margin: 32px 0 24px 0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.wx-login-btn {
  border-radius: 20px; /* 更大的圆角 */
  font-weight: 600;
  font-size: 16px;
  height: 52px; /* 稍微增加高度 */
  line-height: 52px;
  /* 更流畅的过渡动画 */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  /* 增强阴影效果 */
  box-shadow:
    0 6px 20px rgba(0, 82, 217, 0.25),
    0 3px 10px rgba(0, 82, 217, 0.15);
  letter-spacing: 0.8px; /* 增加字间距 */
  /* 添加渐变背景 */
  background: linear-gradient(135deg, #0052D9 0%, #1890ff 100%) !important;
  /* 微妙的边框 */
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.wx-login-btn:active {
  transform: translateY(3px) scale(0.98); /* 增强按压效果 */
  box-shadow:
    0 3px 12px rgba(0, 82, 217, 0.4),
    0 1px 6px rgba(0, 82, 217, 0.2);
  /* 按下时稍微改变渐变 */
  background: linear-gradient(135deg, #003d9e 0%, #1677cc 100%) !important;
}

/* 重置button默认样式 */
.wx-login-btn::after {
  border: none;
}

/* 个人资料区域样式 */
.profile-section {
  width: 100%;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 卡片样式 - 增强视觉效果 */
.profile-card {
  background: #ffffff;
  border-radius: 20rpx; /* 增大圆角，更现代的设计 */
  margin-bottom: 32rpx;
  /* 多层阴影效果，营造更强的立体感 */
  box-shadow:
    0 4rpx 20rpx rgba(0, 0, 0, 0.08),
    0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  /* 添加过渡动画，让交互更流畅 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* 微妙的边框，增强卡片边界 */
  border: 1rpx solid rgba(0, 0, 0, 0.02);
}

/* 卡片悬浮效果（虽然小程序没有hover，但可以用于未来扩展） */
.profile-card:hover {
  transform: translateY(-2rpx);
  box-shadow:
    0 8rpx 30rpx rgba(0, 0, 0, 0.12),
    0 4rpx 12rpx rgba(0, 0, 0, 0.06);
}

.profile-card.student,
.profile-card.coach,
.profile-card.admin {
  border-top: none;
}
/*
  左侧色块实现说明：
  通过 .card-header 的 ::before 伪元素生成一个宽度为 8rpx 的竖色块，
  位置绝对定位于 header 左侧，高度与 header 匹配，圆角与 header 保持一致。
  这样无需额外 view 标签，结构更简洁，且易于维护和统一风格。
  不同卡片通过不同父类（如 .student/.coach/.admin/.help/.info）控制色块颜色。
*/
.profile-card.student .card-header::before {
  content: '';
  position: absolute;
  left: 0; top: 0; bottom: 0;
  width: 8rpx;
  background: #FFAA00;
  border-radius: 8rpx;
}
.profile-card.coach .card-header::before {
  content: '';
  position: absolute;
  left: 0; top: 0; bottom: 0;
  width: 8rpx;
  background: #36B37E;
  border-radius: 8rpx;
}
.profile-card.admin .card-header::before {
  content: '';
  position: absolute;
  left: 0; top: 0; bottom: 0;
  width: 8rpx;
  background: #0052D9;
  border-radius: 8rpx;
}
.profile-card.help .card-header::before {
  content: '';
  position: absolute;
  left: 0; top: 0; bottom: 0;
  width: 8rpx;
  background: #00B8D9;
  border-radius: 8rpx;
}
.profile-card.info .card-header::before {
  content: '';
  position: absolute;
  left: 0; top: 0; bottom: 0;
  width: 8rpx;
  background: #36B37E;
  border-radius: 8rpx;
}
.card-header {
  cursor: default;
  position: relative;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 32rpx 32rpx 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 用户信息卡片头部特殊样式 - 支持右侧通知图标 */
.profile-card.info .card-header {
  justify-content: space-between; /* 两端对齐，左侧标题，右侧通知图标 */
}

/* 卡片头部左侧区域 */
.card-header-left {
  display: flex;
  align-items: center;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-left: 16rpx;

}

.card-content {
  padding-left: 32rpx;
  padding-right: 32rpx;
  margin-bottom: 8rpx;

}
.card-content-userprofile {
 padding: 32rpx;
}

.user-info {
  display: flex;
  align-items: center;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  padding-bottom: 32rpx;
  padding-top: 32rpx;
}

.user-details {
  margin-left: 16px;
  flex: 1;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.user-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.user-role {
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 功能区域样式 */
.function-section {
  margin-bottom: 16px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.function-group {
  margin-bottom: 16px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.group-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  padding-left: 8px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  position: relative;
}

.group-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: linear-gradient(135deg, #0052d9, #1890ff);
  border-radius: 2px;
}

.function-list {
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.function-item {
  display: flex;
  align-items: center;
  padding: 28rpx 0; /* 增加内边距，让点击区域更大 */
  border-bottom: 1rpx solid #f0f0f0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  /* 更流畅的过渡动画 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  /* 添加圆角，让每个功能项更独立 */
  border-radius: 12rpx;
  margin: 4rpx 0;
  /* 微妙的背景渐变 */
  background: linear-gradient(90deg, transparent 0%, rgba(0, 82, 217, 0.01) 100%);
}

.function-item:last-child {
  border-bottom: none;
}

/* 点击时的反馈效果 */
.function-item:active {
  background: linear-gradient(90deg, rgba(0, 82, 217, 0.05) 0%, rgba(0, 82, 217, 0.02) 100%);
  transform: translateX(4rpx) scale(0.98); /* 轻微缩放和位移 */
  /* 添加内阴影效果 */
  box-shadow: inset 0 2rpx 4rpx rgba(0, 82, 217, 0.1);
}

.function-item t-icon:first-child {
  margin-right: 12px;
  color: #0052d9;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.function-item text {
  flex: 1;
  font-size: 32rpx;
  color: #333;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  font-weight: 500;
}

.function-item-label {
  margin-left: 16rpx;
}

.function-item t-icon:last-child {
  color: #ccc;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 退出登录区域样式 */
.logout-section {
  margin-top: 32px;
  margin-bottom: 20px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.logout-btn {
  display: block;
  margin: 40rpx auto 0 auto;
  width: 80%;
  border-radius: 24rpx;
  font-weight: 700;
  font-size: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(227,77,89,0.08);
  background: #fff0f0;
  color: #e34d59;
  border: 2rpx solid #e34d59;
  transition: background 0.2s, color 0.2s;
}
.logout-btn:active {
  background: #e34d59;
  color: #fff;
}

.avatar-btn {
  padding: 0;
  border: none;
  background: none;
  outline: none;
  box-shadow: none;
  display: inline-block;
}

.avatar-wrapper {
  position: relative;
  display: inline-block;
}
.avatar-btn-overlay {
  position: absolute;
  left: 0; top: 0;
  width: 60px; height: 60px;
  opacity: 0;
  padding: 0;
  border: none;
  background: none;
  z-index: 2;
}

.agreement-link {
  color: #0052d9;
  text-decoration: underline;
  text-underline-offset: 2px;
  text-decoration-thickness: 1px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.agreement-link:hover {
  color: #003d9e;
  text-decoration-color: #003d9e;
}

.agreement-link:active {
  color: #002a7a;
  text-decoration-color: #002a7a;
}

.agreement-section {
  background: transparent !important;
  padding: 0;
  margin-top: 16px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.agreement-section t-checkbox,
.agreement-section .t-checkbox {
  background: transparent !important;
}

/*
  通知图标相关样式
  用于个人中心页面的通知入口
*/

/* 通知图标包装器 */
.notification-icon-wrapper {
  position: relative; /* 相对定位，为红点提供定位基准 */
  padding: 8rpx; /* 增加点击区域 */
  cursor: pointer; /* 鼠标指针样式 */
  transition: opacity 0.2s ease; /* 点击时的透明度过渡 */
}

/* 通知图标点击效果 */
.notification-icon-wrapper:active {
  opacity: 0.6; /* 点击时降低透明度，提供视觉反馈 */
}

/* 通知红点徽章 */
.notification-badge {
  position: absolute; /* 绝对定位，相对于父容器 */
  top: 0; /* 距离顶部0距离 */
  right: 0; /* 距离右侧0距离 */
  min-width: 32rpx; /* 最小宽度，确保圆形 */
  height: 32rpx; /* 固定高度 */
  background-color: #ff4757; /* 红色背景 */
  border-radius: 16rpx; /* 圆角，形成圆形或胶囊形 */
  display: flex; /* 弹性布局 */
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  border: 2rpx solid #ffffff; /* 白色边框，与背景分离 */
  box-sizing: border-box; /* 边框计入尺寸 */
}

/* 红点内的数字文字 */
.badge-text {
  font-size: 20rpx; /* 小字体 */
  color: #ffffff; /* 白色文字 */
  font-weight: 600; /* 加粗字重 */
  line-height: 1; /* 行高1，确保垂直居中 */
  padding: 0 6rpx; /* 左右内边距，为长数字提供空间 */
  white-space: nowrap; /* 不换行 */
}

.agreement-section text {
  font-size: 14px;
  line-height: 1.5;
  color: #666666;
  font-weight: 400;
}

/* 页面装饰元素 */
.container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  background: linear-gradient(135deg, rgba(0, 82, 217, 0.03) 0%, transparent 70%);
  pointer-events: none; /* 不影响用户交互 */
  z-index: 0;
}

.container::after {
  content: '';
  position: fixed;
  bottom: 0;
  right: 0;
  width: 300rpx;
  height: 300rpx;
  background: radial-gradient(circle, rgba(54, 179, 126, 0.02) 0%, transparent 70%);
  pointer-events: none;
  z-index: 0;
}

/* 确保内容在装饰元素之上 */
.login-section,
.profile-section {
  position: relative;
  z-index: 1;
}

/* 角色标签美化 */
.user-role .t-tag {
  /* 添加微妙的渐变背景 */
  background: linear-gradient(135deg, rgba(0, 82, 217, 0.1) 0%, rgba(0, 82, 217, 0.05) 100%) !important;
  /* 增强边框 */
  border: 1rpx solid rgba(0, 82, 217, 0.2) !important;
  /* 添加阴影 */
  box-shadow: 0 2rpx 4rpx rgba(0, 82, 217, 0.1) !important;
}

/* 卡片入场动画 */
@keyframes cardSlideIn {
  0% {
    opacity: 0;
    transform: translateY(30rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.card-animate {
  animation: cardSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* 为不同的卡片添加延迟，创造层次感 */
.profile-card:nth-child(1) { animation-delay: 0.1s; }
.profile-card:nth-child(2) { animation-delay: 0.2s; }
.profile-card:nth-child(3) { animation-delay: 0.3s; }
.profile-card:nth-child(4) { animation-delay: 0.4s; }
.profile-card:nth-child(5) { animation-delay: 0.5s; }

/* 功能项图标旋转动画 */
@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.function-item:active t-icon:first-child {
  animation: iconPulse 0.3s ease;
}

/* Logo呼吸动画 */
@keyframes logoBreath {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
}

.logo {
  animation: logoBreath 3s ease-in-out infinite;
}

/* 状态指示器样式 */
.status-indicator {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx); /* 毛玻璃效果 */
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: 8rpx;
  animation: statusPulse 2s ease-in-out infinite;
}

.status-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #333;
}

/* 不同状态的颜色 */
.student-status .status-dot {
  background: #FFAA00;
  box-shadow: 0 0 10rpx rgba(255, 170, 0, 0.5);
}

.coach-status .status-dot {
  background: #36B37E;
  box-shadow: 0 0 10rpx rgba(54, 179, 126, 0.5);
}

.admin-status .status-dot {
  background: #0052D9;
  box-shadow: 0 0 10rpx rgba(0, 82, 217, 0.5);
}

/* 状态点脉冲动画 */
@keyframes statusPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

/* 浮动装饰元素 */
.floating-decorations {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none; /* 不影响用户交互 */
  z-index: 0;
  overflow: hidden;
}

.floating-dot {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(0, 82, 217, 0.1), rgba(54, 179, 126, 0.1));
  animation: float 6s ease-in-out infinite;
}

.dot-1 {
  width: 60rpx;
  height: 60rpx;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.dot-2 {
  width: 40rpx;
  height: 40rpx;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.dot-3 {
  width: 80rpx;
  height: 80rpx;
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
}

.dot-4 {
  width: 30rpx;
  height: 30rpx;
  top: 40%;
  right: 30%;
  animation-delay: 1s;
}

/* 浮动动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.3;
  }
  33% {
    transform: translateY(-20rpx) rotate(120deg);
    opacity: 0.6;
  }
  66% {
    transform: translateY(10rpx) rotate(240deg);
    opacity: 0.4;
  }
}