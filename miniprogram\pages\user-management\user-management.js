// pages/user-management/user-management.js
// 用户管理页面逻辑文件
// 这是小程序的用户管理页面，负责管理所有用户信息、角色分配、权限控制等功能
// 类似于Web应用的后台用户管理系统或移动应用的管理员面板

/**
 * 模块导入说明
 *
 * 用户管理页面的特点：
 * 1. 高权限要求：只有管理员可以访问
 * 2. 复杂操作：用户增删改查、角色管理、权限分配
 * 3. 数据安全：涉及用户隐私和系统安全
 * 4. 批量操作：支持批量用户管理
 * 5. 实时同步：用户信息的实时更新
 *
 * 安全考虑：
 * - 严格的权限验证
 * - 敏感操作的二次确认
 * - 操作日志记录
 * - 数据备份和恢复
 */

// 导入Toast工具函数，用于操作反馈
import { showToast, showLoading, hideToast } from '../../utils/toast.js';

/**
 * Page()函数：注册用户管理页面
 *
 * 页面功能：
 * 1. 用户列表展示：分页显示所有用户信息
 * 2. 用户搜索：按姓名、角色等条件搜索用户
 * 3. 角色管理：修改用户角色（学员、讲师、管理员）
 * 4. 讲师信息维护：管理讲师的专业信息
 * 5. 用户删除：删除不活跃或违规用户
 * 6. 权限控制：确保操作安全性
 *
 * 设计模式：
 * - 列表 + 详情的经典管理界面
 * - 弹窗式编辑，减少页面跳转
 * - 确认机制，防止误操作
 * - 分页加载，优化性能
 */
Page({
  /**
   * data: 页面数据对象
   *
   * 数据结构设计：
   * 1. 用户数据：用户列表、筛选结果
   * 2. UI状态：加载状态、弹窗状态
   * 3. 搜索功能：搜索关键词、筛选条件
   * 4. 编辑功能：角色编辑、讲师信息编辑
   * 5. 删除功能：删除确认、倒计时
   * 6. 分页功能：页码、页面大小、加载状态
   */
  data: {
    /**
     * Tab切换和用户数据
     *
     * 用户管理页面的核心数据结构
     * 支持按角色分类查看用户，提供清晰的管理界面
     */

    // 当前激活的Tab：控制用户列表的筛选条件
    // 数据类型：string
    // 可选值：
    //   - 'all'：显示所有用户，不区分角色
    //   - 'student'：只显示学员用户
    //   - 'coach'：只显示讲师用户
    //   - 'admin'：只显示管理员用户
    // 默认值：'all' - 初始显示所有用户
    // 更新时机：用户点击Tab切换时更新
    // 影响范围：影响filteredUserList的筛选结果
    activeTab: 'all',

    // 用户列表（原始数据）：存储从数据库获取的完整用户信息
    // 数据类型：Array<UserObject>
    // 数据结构：[{openid, nickName, avatarUrl, roles, createTime, lastLoginTime, ...}]
    // 数据来源：loadUsers()方法从users集合查询获得
    // 特点：包含所有用户的完整信息，不经过筛选
    // 用途：作为筛选和搜索的数据源
    // 更新时机：页面加载、下拉刷新、用户信息修改后
    userList: [],

    // 过滤后的用户列表（显示数据）：经过筛选和搜索后的用户数据
    // 数据类型：Array<UserObject>
    // 数据来源：userList经过Tab筛选和搜索关键词过滤
    // 特点：这是实际渲染到页面上的数据
    // 筛选条件：
    //   1. 角色筛选：根据activeTab筛选特定角色的用户
    //   2. 关键词搜索：根据searchValue搜索用户姓名
    //   3. 分页处理：根据page和pageSize进行分页
    // 更新时机：Tab切换、搜索输入、数据加载时
    filteredUserList: [],

    /**
     * 分页和加载状态
     *
     * 用户管理页面采用分页加载机制
     * 避免一次性加载大量用户数据，提升性能和用户体验
     */

    // 加载状态标识：控制loading动画的显示
    // 数据类型：boolean
    // true：正在加载数据，显示loading动画，禁用用户操作
    // false：加载完成，隐藏loading动画，允许用户操作
    // 设置时机：
    //   - 开始加载时设为true
    //   - 加载完成（成功或失败）后设为false
    // 用户体验：防止用户在数据未加载完成时进行操作
    loading: false,

    // 是否还有更多用户数据：控制分页加载的边界条件
    // 数据类型：boolean
    // true：还有更多数据可以加载，显示"加载更多"按钮
    // false：已加载完所有数据，显示"没有更多数据"提示
    // 判断逻辑：当某次查询返回的数据量小于pageSize时，设为false
    // 用途：控制分页UI的显示状态
    userHasMore: true,

    // 当前页码：记录分页加载的当前位置
    // 数据类型：number
    // 初始值：1 - 从第一页开始加载
    // 递增规则：每次加载更多数据时页码+1
    // 重置时机：重新搜索或刷新数据时重置为1
    // 用途：计算数据库查询的skip参数（跳过的记录数）
    page: 1,

    // 每页数据条数：控制分页加载的粒度
    // 数据类型：number
    // 默认值：20 - 平衡加载速度和用户体验
    // 考虑因素：
    //   - 太小：频繁加载，增加网络请求次数
    //   - 太大：单次加载时间长，影响用户体验
    // 用途：数据库查询的limit参数（返回记录数限制）
    pageSize: 20,

    /**
     * 搜索功能相关
     *
     * 支持按用户姓名搜索，提供快速查找用户的能力
     * 实现实时搜索，用户输入时立即筛选结果
     */

    // 搜索关键词：用户输入的搜索内容
    // 数据类型：string
    // 默认值：空字符串 - 不进行搜索筛选
    // 更新时机：用户在搜索框中输入时实时更新
    // 搜索范围：用户的昵称（nickName字段）
    // 搜索方式：模糊匹配，不区分大小写
    // 清空时机：用户点击清空按钮或切换Tab时
    searchValue: '',

    /**
     * 删除用户功能相关
     *
     * 安全机制设计：
     * 删除用户是高风险操作，涉及数据安全和用户隐私
     * 采用多重确认机制防止误操作：
     * 1. 弹窗确认：显示删除确认对话框
     * 2. 倒计时延迟：强制用户等待5秒思考时间
     * 3. 二次确认按钮：倒计时结束后才能点击确认
     * 4. 详细提示：显示被删除用户的详细信息
     *
     * 业务考虑：
     * - 删除用户会同时删除其预约记录
     * - 如果是讲师，需要处理其开设的课程
     * - 删除操作不可逆，需要谨慎处理
     */

    // 删除确认弹窗显示状态：控制删除确认对话框的显示
    // 数据类型：boolean
    // true：显示删除确认弹窗，用户可以确认或取消删除操作
    // false：隐藏删除确认弹窗
    // 触发时机：用户点击删除按钮时设为true
    // 关闭时机：用户确认删除、取消删除或点击弹窗外部时设为false
    deleteDialogVisible: false,

    // 删除确认弹窗内容：显示在确认对话框中的提示文字
    // 数据类型：string
    // 内容格式：包含被删除用户的姓名和角色信息
    // 示例：'确定要删除用户"张三"吗？该用户的角色是：学员'
    // 动态生成：根据deleteUserData中的用户信息动态生成
    // 用途：让管理员清楚知道要删除的是哪个用户
    deleteDialogContent: '',

    // 删除确认按钮文字：确认按钮显示的文字内容
    // 数据类型：string
    // 动态变化：
    //   - 倒计时期间：'确认删除(5)' -> '确认删除(4)' -> ... -> '确认删除(1)'
    //   - 倒计时结束：'确认删除'
    // 默认值：'确认删除' - 倒计时结束后的最终状态
    // 用途：提供视觉反馈，告知用户当前的操作状态
    deleteDialogConfirmBtn: '确认删除',

    // 待删除的用户数据：存储即将被删除的用户完整信息
    // 数据类型：Object | null
    // 数据结构：{openid, nickName, avatarUrl, roles, ...}
    // 数据来源：用户点击删除按钮时，从用户列表中获取对应的用户对象
    // null状态：没有用户等待删除时为null
    // 用途：
    //   1. 生成删除确认弹窗的提示内容
    //   2. 执行删除操作时提供用户标识
    //   3. 删除完成后从列表中移除对应项
    deleteUserData: null,

    // 删除倒计时秒数：防止误操作的安全倒计时
    // 数据类型：number
    // 初始值：5 - 用户需要等待5秒才能确认删除
    // 递减规则：每秒减1，直到为0
    // 重置时机：每次打开删除确认弹窗时重置为5
    // 安全机制：强制用户有足够的时间思考删除操作的后果
    // 用户体验：在按钮文字中显示剩余秒数，提供清晰的反馈
    deleteCountdown: 5,

    // 删除倒计时定时器：控制倒计时的定时器对象
    // 数据类型：Timer | null
    // 创建时机：打开删除确认弹窗时创建
    // 清除时机：倒计时结束、用户取消删除、确认删除时清除
    // 用途：每秒更新deleteCountdown的值和按钮文字
    // 内存管理：及时清除定时器，避免内存泄漏
    deleteCountdownTimer: null,

    /**
     * 角色编辑功能相关
     *
     * 角色系统设计：
     * 健身房管理系统采用基于角色的权限控制（RBAC）
     * 每个用户可以拥有一个或多个角色，不同角色有不同的权限
     *
     * 角色定义：
     * - 学员：基础用户角色
     *   权限：预约课程、查看自己的预约记录、修改个人信息
     * - 讲师：专业服务提供者角色
     *   权限：开设课程、管理自己的课程、查看学员列表、讲师权限+学员权限
     * - 管理员：系统管理者角色
     *   权限：管理用户、管理课程、系统设置、查看所有数据、所有权限
     *
     * 多角色支持：
     * 一个用户可以同时拥有多个角色，例如：
     * - 讲师+学员：既可以开课也可以参加其他讲师的课程
     * - 管理员+讲师：既有管理权限也可以开设课程
     */

    // 角色编辑弹窗显示状态：控制角色编辑对话框的显示
    // 数据类型：boolean
    // true：显示角色编辑弹窗，管理员可以修改用户角色
    // false：隐藏角色编辑弹窗
    // 触发时机：管理员点击用户的"编辑角色"按钮时设为true
    // 关闭时机：保存角色、取消编辑或点击弹窗外部时设为false
    editRolePopupVisible: false,

    // 正在编辑角色的用户数据：存储当前正在编辑角色的用户信息
    // 数据类型：Object | null
    // 数据结构：{openid, nickName, avatarUrl, roles, ...}
    // 数据来源：管理员点击"编辑角色"按钮时，从用户列表中获取对应用户
    // null状态：没有用户正在编辑角色时为null
    // 用途：
    //   1. 在角色编辑弹窗中显示用户基本信息
    //   2. 初始化角色复选框的选中状态
    //   3. 保存角色时提供用户标识
    editRoleUserData: null,

    // 角色选择复选框配置：定义所有可用的角色选项
    // 数据类型：Array<CheckboxOption>
    // 数据结构：[{label: '显示名称', value: '角色值', checked: boolean}]
    // 用途：渲染角色编辑弹窗中的复选框列表
    // 动态更新：根据当前编辑用户的角色信息更新checked状态
    roleCheckboxes: [
      {
        label: '学员',
        value: '学员',
        checked: true,      // 默认选中（所有用户都是学员）
        disabled: true      // 禁用（学员角色不能取消）
      },
      {
        label: '讲师',
        value: '讲师',
        checked: false,     // 默认不选中
        disabled: false     // 可以选择
      },
      {
        label: '管理员',
        value: '管理员',
        checked: false,     // 默认不选中
        disabled: true      // 禁用（管理员角色需要特殊权限分配）
      }
    ],

    // 选中的角色值数组
    selectedRoleValues: ['学员'],

    /**
     * 讲师信息维护功能相关
     *
     * 讲师专业信息：
     * 当用户被设置为讲师角色时，需要维护专业信息
     * 包括个人简介、专长领域等
     */

    // 讲师信息编辑弹窗显示状态
    coachInfoPopupVisible: false,

    // 正在编辑讲师信息的用户数据
    coachInfoUserData: null,

    // 讲师信息对象
    coachInfo: {
      introduction: '',    // 个人简介
      specialties: ''      // 专长领域（逗号分隔）
    }
  },

  onLoad() {
    this.setData({
      editRolePopupVisible: false
    });
    
    // 直接加载用户列表
    showLoading(this, '加载中...');
    this.loadUserList();
  },

  onShow() {
    // 只在用户列表为空时才重新加载数据
    if (this.data.userList.length === 0) {
      this.loadUserList();
    }
  },

  onUnload() {
    // 清理定时器
    this.clearDeleteCountdown();
  },

  onTabChange(e) {
    // t-tabs 传递的 value 是英文（all/student/coach/admin），需映射为中文角色
    // coach=讲师
    const tabValue = e.detail.value;
    let role = '';
    if (tabValue === 'student') role = '学员';
    else if (tabValue === 'coach') role = '讲师';
    else if (tabValue === 'admin') role = '管理员';
    // 记录当前tab
    this.setData({ 
      activeTab: tabValue,
      page: 1,
      userHasMore: true
    }, () => this.loadUserList(role));
  },

  /**
   * loadUserList: 加载用户列表的异步方法
   *
   * 功能说明：
   * 从云数据库获取用户列表数据，支持分页加载和角色筛选
   * 这是用户管理页面的核心数据加载方法
   *
   * @param {string} role - 角色筛选条件，'all'表示不筛选
   * @param {boolean} isLoadMore - 是否为加载更多操作，默认false
   *
   * 业务流程：
   * 1. 设置加载状态
   * 2. 构建查询参数
   * 3. 调用云函数查询数据
   * 4. 处理返回数据
   * 5. 更新页面状态
   * 6. 触发数据筛选
   */
  async loadUserList(role, isLoadMore = false) {

    /**
     * 第一步：设置加载状态
     *
     * 条件判断：只有非"加载更多"操作才显示loading
     * 原因：加载更多时页面底部有专门的加载提示，不需要全页面loading
     */
    if (!isLoadMore) {
      // 显示全页面loading动画：提示用户数据正在加载
      this.setData({ loading: true });
    }

    try {
      /**
       * 第二步：构建查询参数
       *
       * 查询参数设计：
       * - page: 当前页码，用于分页查询
       * - pageSize: 每页数据量，控制单次查询的数据量
       * - role: 角色筛选条件，可选参数
       */

      // 基础查询参数：分页相关参数
      let queryData = {
        page: this.data.page,        // 当前页码：从1开始，每次加载更多时递增
        pageSize: this.data.pageSize  // 每页大小：控制单次查询的用户数量
      };

      // 角色筛选参数：根据Tab选择添加角色筛选条件
      // 条件：role存在且不是'all'（全部）
      if (role && role !== 'all') {
        queryData.role = role;  // 添加角色筛选：只查询特定角色的用户
      }

      /**
       * 第三步：调用云函数查询数据
       *
       * 云函数优势：
       * 1. 服务端权限验证：确保只有管理员可以获取用户列表
       * 2. 数据安全：敏感用户信息不直接暴露给客户端
       * 3. 复杂查询：支持复杂的数据库查询和聚合操作
       * 4. 性能优化：服务端处理数据，减少客户端计算负担
       */
      const res = await wx.cloud.callFunction({
        name: 'adminManagement',    // 云函数名称：管理员功能云函数
        data: {
          action: 'getUserList',    // 操作类型：获取用户列表
          data: queryData          // 查询参数：分页和筛选条件
        }
      });

      /**
       * 第四步：处理返回数据
       *
       * 数据验证：确保返回数据的格式正确
       * 防御性编程：处理可能的数据异常情况
       */
      if (res && res.result && res.result.success) {
        // 提取用户数据：使用默认值防止undefined错误
        const users = res.result.data || [];

        // 提取分页信息：判断是否还有更多数据
        const hasMore = res.result.hasMore || false;

        /**
         * 数据预处理：格式化和增强用户数据
         *
         * 处理内容：
         * 1. 时间格式化：将时间戳转换为可读的日期格式
         * 2. 计算属性：添加便于模板使用的计算属性
         */
        users.forEach(u => {
          // 格式化创建时间：转换为 YYYY-MM-DD HH:mm:ss 格式
          u.createTime = this.formatDate(u.createTime);

          // 格式化最后登录时间：转换为可读的日期格式
          u.lastLoginTime = this.formatDate(u.lastLoginTime);

          // 添加讲师标识计算属性：便于模板中的条件判断
          // 判断逻辑：检查用户角色数组中是否包含'讲师'角色
          u.isCoach = u.roles && u.roles.indexOf('讲师') >= 0;
        });

        /**
         * 第五步：更新页面状态
         *
         * 分两种情况处理：
         * 1. 加载更多：追加新数据到现有列表
         * 2. 首次加载/刷新：替换整个列表
         */
        if (isLoadMore) {
          /**
           * 加载更多数据的处理
           *
           * 数据合并：使用扩展运算符合并数组
           * 状态更新：更新分页相关状态
           * 回调处理：数据更新完成后触发筛选
           */
          this.setData({
            // 合并用户列表：将新数据追加到现有数据后面
            userList: [...this.data.userList, ...users],

            // 更新分页状态：是否还有更多数据可加载
            userHasMore: hasMore,

            // 递增页码：为下次加载更多做准备
            page: this.data.page + 1
          }, () => {
            // 回调函数：数据更新完成后执行筛选
            // 确保筛选操作基于最新的数据
            this._filterAndSetUsers();
          });
        } else {
          // 首次加载或刷新时替换数据
          this.setData({ 
            userList: users,
            userHasMore: hasMore,
            page: 2 // 下次加载更多时从第2页开始
          }, () => {
            this._filterAndSetUsers();
          });
        }
        
      } else {
        const errorMsg = res.result?.message || '获取用户失败';
        if (!isLoadMore) {
          this.setData({ userList: [] });
        }
        // 只在非下拉刷新时显示错误提示
        if (!this.isRefreshing) {
          showToast(this, { message: errorMsg, theme: 'error' });
        }
      }
    } catch (e) {
      if (!isLoadMore) {
        this.setData({ userList: [] });
      }
      // 只在非下拉刷新时显示错误提示
      if (!this.isRefreshing) {
        showToast(this, { message: '获取用户失败', theme: 'error' });
      }
    }
    
    this.setData({ loading: false });
    hideToast(this);
  },

  // 触底加载更多
  onReachBottom() {
    if (this.data.loading || !this.data.userHasMore) return;
    
    const currentRole = this.getCurrentRole();
    this.loadUserList(currentRole, true);
  },

  formatDate(date) {
    if (!date) {
      return '';
    }
    
    if (typeof date === 'string') {
      // 如果是字符串，尝试解析并格式化
      try {
        const dateObj = new Date(date);
        if (!isNaN(dateObj.getTime())) {
          const result = this.formatDateTime(dateObj);
          return result;
        }
      } catch (e) {
        // 如果解析失败，返回原字符串的前16个字符（YYYY-MM-DD HH:MM）
        const result = date.slice(0, 16);
        return result;
      }
      // 如果解析失败，返回原字符串的前16个字符（YYYY-MM-DD HH:MM）
      const result = date.slice(0, 16);
      return result;
    }
    
    if (date instanceof Date) {
      const result = this.formatDateTime(date);
      return result;
    }
    
    if (date.$date) {
      const dateObj = new Date(date.$date);
      const result = this.formatDateTime(dateObj);
      return result;
    }
    
    // 尝试其他可能的格式
    try {
      const dateObj = new Date(date);
      if (!isNaN(dateObj.getTime())) {
        const result = this.formatDateTime(dateObj);
        return result;
      }
    } catch (e) {
      // 日期格式化失败
    }
    
    return '';
  },

  // 格式化日期时间
  formatDateTime(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },

  mapRoleClass(role) {
    if (role === '学员') return 'student';
    if (role === '讲师') return 'coach'; // coach=讲师
    if (role === '管理员') return 'admin';
    return 'other';
  },

  mapRoleTheme(role) {
    if (role === '学员') return 'primary';
    if (role === '讲师') return 'success';
    if (role === '管理员') return 'warning';
    return 'default';
  },

  // 用户卡片点击
  onUserCardTap(e) {
    const user = e.currentTarget.dataset.user;
    // 可以在这里添加用户详情页面跳转
  },

  // 头像加载错误处理
  onAvatarError(e) {
    const index = e.currentTarget.dataset.index;
    const userList = this.data.userList;
    if (userList[index]) {
      userList[index].avatarUrl = ''; // 清空头像URL，让组件显示默认头像
      this.setData({ userList });
    }
  },

  onEditRole(e) {
    const id = e.currentTarget.dataset.id;
    
    // 查找用户信息
    const user = this.data.userList.find(u => u._id === id);
    if (!user) {
      showToast(this, { message: '用户信息错误', theme: 'error' });
      return;
    }
    
    // 初始化复选框状态
    const roleCheckboxes = this.data.roleCheckboxes.map(checkbox => {
      const checked = user.roles && user.roles.includes(checkbox.value);
      let disabled = false;
      
      if (checkbox.value === '学员') {
        disabled = true; // 学员始终禁用
      } else if (checkbox.value === '管理员') {
        disabled = true; // 管理员始终禁用（只能由开发者修改）
      } else if (checkbox.value === '讲师') {
        disabled = false; // 讲师可以自由修改
      }
      
      return {
        ...checkbox,
        checked: checked,
        disabled: disabled
      };
    });
    
    // 获取当前选中的角色值（学员始终选中）
    const selectedRoleValues = ['学员'];
    if (user.roles && user.roles.includes('讲师')) {
      selectedRoleValues.push('讲师');
    }
    if (user.roles && user.roles.includes('管理员')) {
      selectedRoleValues.push('管理员');
    }
    
    this.setData({
      editRolePopupVisible: true,
      editRoleUserData: user,
      roleCheckboxes: roleCheckboxes,
      selectedRoleValues: selectedRoleValues
    });
  },

  // 角色复选框变化处理
  onRoleCheckboxChange(e) {
    const selectedValues = e.detail.value || [];
    
    // 确保学员始终被选中
    if (!selectedValues.includes('学员')) {
      selectedValues.push('学员');
    }
    
    // 更新复选框状态
    const roleCheckboxes = this.data.roleCheckboxes.map(checkbox => ({
      ...checkbox,
      checked: selectedValues.includes(checkbox.value)
    }));
    
    this.setData({ 
      roleCheckboxes,
      selectedRoleValues: selectedValues
    });
  },

  // 确认改角色
  async onEditRoleConfirm() {
    const user = this.data.editRoleUserData;
    if (!user) {
      showToast(this, { message: '用户数据错误', theme: 'error' });
      return;
    }
    
    // 获取选中的角色（学员始终被选中）
    const selectedRoles = ['学员']; // 学员始终被选中
    
    // 检查是否选中讲师
    if (this.data.roleCheckboxes.find(cb => cb.value === '讲师').checked) {
      selectedRoles.push('讲师');
    }
    
    // 保持原有的管理员身份（如果有的话）
    if (user.roles && user.roles.includes('管理员')) {
      selectedRoles.push('管理员');
    }
    
    try {
      showLoading(this, '更新中...');
      
      const requestData = {
        action: 'updateUserRoles',
        data: {
          userId: user._id,
          openid: user.openid,
          roles: selectedRoles
        }
      };
      
      const res = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: requestData
      });
      
      if (res && res.result && res.result.success) {
        showToast(this, { message: '角色更新成功', theme: 'success' });
        // 重置分页状态并重新加载用户列表
        this.setData({
          page: 1,
          userHasMore: true
        });
        const currentRole = this.getCurrentRole();
        await this.loadUserList(currentRole, false);
      } else {
        const errorMsg = res.result?.message || '角色更新失败';
        showToast(this, { message: errorMsg, theme: 'error' });
      }
    } catch (error) {
      showToast(this, { message: '角色更新失败', theme: 'error' });
    } finally {
      hideToast(this);
      this.onEditRoleCancel();
    }
  },

  // 取消改角色
  onEditRoleCancel() {
    this.setData({
      editRolePopupVisible: false,
      editRoleUserData: null,
      roleCheckboxes: [
        { label: '学员', value: '学员', checked: true, disabled: true },
        { label: '讲师', value: '讲师', checked: false, disabled: false },
        { label: '管理员', value: '管理员', checked: false, disabled: true }
      ],
      selectedRoleValues: ['学员']
    });
  },

  onDeleteUser(e) {
    const user = e.currentTarget.dataset.user;
    
    // 检查是否为管理员
    if (user.roles && user.roles.includes('管理员')) {
      showToast(this, { message: '不能删除管理员用户', theme: 'error' });
      return;
    }
    
    // 检查用户是否有必要的字段
    if (!user._id || !user.openid) {
      showToast(this, { message: '用户信息不完整，无法删除', theme: 'error' });
      return;
    }
    
    // 构建确认内容
    const content = `确定要删除用户"${user.nickName || '未知用户'}"吗？\n\n⚠️ 警告：此操作不可恢复！\n用户的所有数据将被永久删除。`;
    
    this.setData({
      deleteDialogVisible: true,
      deleteDialogContent: content,
      deleteUserData: user,
      deleteCountdown: 5,
      deleteDialogConfirmBtn: `确认删除 (5s)`
    });
    
    // 开始倒计时
    this.startDeleteCountdown();
  },

  // 开始删除倒计时
  startDeleteCountdown() {
    this.clearDeleteCountdown();
    
    this.data.deleteCountdownTimer = setInterval(() => {
      const countdown = this.data.deleteCountdown - 1;
      
      if (countdown <= 0) {
        this.setData({
          deleteCountdown: 0,
          deleteDialogConfirmBtn: '确认删除'
        });
        this.clearDeleteCountdown();
      } else {
        this.setData({
          deleteCountdown: countdown,
          deleteDialogConfirmBtn: `确认删除 (${countdown}s)`
        });
      }
    }, 1000);
  },

  // 清除删除倒计时
  clearDeleteCountdown() {
    if (this.data.deleteCountdownTimer) {
      clearInterval(this.data.deleteCountdownTimer);
      this.data.deleteCountdownTimer = null;
    }
  },

  // 删除确认
  async onDeleteConfirm() {
    if (this.data.deleteCountdown > 0) {
      showToast(this, { message: '请等待倒计时结束', theme: 'warning' });
      return;
    }
    
    const user = this.data.deleteUserData;
    if (!user) {
      showToast(this, { message: '用户数据错误', theme: 'error' });
      return;
    }
    
    // 再次检查用户信息完整性
    if (!user._id || !user.openid) {
      showToast(this, { message: '用户信息不完整，无法删除', theme: 'error' });
      return;
    }
    
    // 再次检查是否为管理员
    if (user.roles && user.roles.includes('管理员')) {
      showToast(this, { message: '不能删除管理员用户', theme: 'error' });
      return;
    }
    
    try {
      showLoading(this, '删除中...');
      
      const requestData = {
        action: 'deleteUser',
        data: {
          userId: user._id,
          openid: user.openid
        }
      };
      
      const res = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: requestData
      });
      
      if (res && res.result && res.result.success) {
        showToast(this, { message: '删除成功', theme: 'success' });
        // 重置分页状态并重新加载用户列表
        this.setData({
          page: 1,
          userHasMore: true
        });
        const currentRole = this.getCurrentRole();
        await this.loadUserList(currentRole, false);
      } else {
        const errorMsg = res.result?.message || '删除失败';
        if (errorMsg.includes('会员卡')) {
          showToast(this, { 
            message: '该用户有关联的会员卡，请先处理会员卡', 
            theme: 'error',
            duration: 4000
          });
        } else if (errorMsg.includes('预约记录')) {
          showToast(this, { 
            message: '该用户有预约记录，请先处理预约', 
            theme: 'error',
            duration: 4000
          });
        } else if (errorMsg.includes('管理员')) {
          showToast(this, { 
            message: '不能删除管理员用户', 
            theme: 'error',
            duration: 4000
          });
        } else {
          showToast(this, { message: errorMsg, theme: 'error' });
        }
      }
    } catch (error) {
      showToast(this, { message: '删除失败', theme: 'error' });
    } finally {
      hideToast(this);
      this.onDeleteCancel();
    }
  },

  // 删除取消
  onDeleteCancel() {
    this.clearDeleteCountdown();
    this.setData({
      deleteDialogVisible: false,
      deleteDialogContent: '',
      deleteUserData: null,
      deleteCountdown: 5,
      deleteDialogConfirmBtn: '确认删除'
    });
  },

  // 复制 openid
  onCopyOpenid(e) {
    const openid = e.currentTarget.dataset.openid;
    if (!openid) {
      showToast(this, { message: 'openid 为空', theme: 'error' });
      return;
    }

    // 复制到剪贴板
    wx.setClipboardData({
      data: openid,
      showToast: false, // 禁用系统默认提示
      success: () => {
        // 延迟显示自定义提示，确保系统提示完全被禁用
        setTimeout(() => {
          showToast(this, { message: '复制成功', theme: 'success' });
        }, 100);
      },
      fail: () => {
        showToast(this, { message: '复制失败', theme: 'error' });
      }
    });
  },

  // 下拉刷新
  async onPullDownRefresh() {
    try {
      this.isRefreshing = true;
      // 重置分页状态
      this.setData({
        page: 1,
        userHasMore: true
      });
      
      // 根据当前选中的标签页重新加载数据
      const currentRole = this.getCurrentRole();
      await this.loadUserList(currentRole);
      
      // 显示刷新成功提示
      showToast(this, { message: '刷新成功', theme: 'success' });
    } catch (error) {
      showToast(this, { message: '刷新失败', theme: 'error' });
    } finally {
      this.isRefreshing = false;
      // 停止下拉刷新动画
      wx.stopPullDownRefresh();
    }
  },

  // 获取当前角色
  getCurrentRole() {
    const { activeTab } = this.data;
    switch (activeTab) {
      case 'student':
        return '学员';
      case 'coach':
        return '讲师'; // coach=讲师
      case 'admin':
        return '管理员';
      default:
        return '';
    }
  },

  // 编辑讲师信息
  async onEditCoachInfo(e) {
    const user = e.currentTarget.dataset.user;
    if (!user) {
      showToast(this, { message: '用户信息错误', theme: 'error' });
      return;
    }

    try {
      showLoading(this, '加载中...');
      
      // 获取讲师信息
      const res = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'getCoachInfo',
          data: { openid: user.openid }
        }
      });

      let coachInfo = {
        introduction: '',
        specialties: ''
      };

      if (res && res.result && res.result.success && res.result.data) {
        coachInfo = {
          introduction: res.result.data.introduction || '',
          specialties: res.result.data.specialties || ''
        };
      }

      this.setData({
        coachInfoPopupVisible: true,
        coachInfoUserData: user,
        coachInfo: coachInfo
      });
    } catch (error) {
      showToast(this, { message: '加载讲师信息失败', theme: 'error' });
    } finally {
      hideToast(this);
    }
  },

  // 讲师信息输入变化
  onCoachInfoChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`coachInfo.${field}`]: value
    });
  },

  // 确认保存讲师信息
  async onCoachInfoConfirm() {
    const user = this.data.coachInfoUserData;
    const coachInfo = this.data.coachInfo;
    
    if (!user) {
      showToast(this, { message: '用户数据错误', theme: 'error' });
      return;
    }

    try {
      showLoading(this, '保存中...');
      
      const res = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'updateCoachInfo',
          data: {
            openid: user.openid,
            introduction: coachInfo.introduction,
            specialties: coachInfo.specialties
          }
        }
      });

      if (res && res.result && res.result.success) {
        showToast(this, { message: '讲师信息保存成功', theme: 'success' });
        this.onCoachInfoCancel();
        // 重置分页状态并重新加载用户列表
        this.setData({
          page: 1,
          userHasMore: true
        });
        const currentRole = this.getCurrentRole();
        await this.loadUserList(currentRole, false);
      } else {
        const errorMsg = res.result?.message || '保存失败';
        showToast(this, { message: errorMsg, theme: 'error' });
      }
    } catch (error) {
      showToast(this, { message: '保存失败', theme: 'error' });
    } finally {
      hideToast(this);
    }
  },

  // 取消编辑讲师信息
  onCoachInfoCancel() {
    this.setData({
      coachInfoPopupVisible: false,
      coachInfoUserData: null,
      coachInfo: {
        introduction: '',
        specialties: ''
      }
    });
  },

  // 搜索输入变化
  onSearchInput(e) {
    const value = e.detail.value;
    this.setData({ searchValue: value }, () => {
      this._filterAndSetUsers();
    });
  },



  // 清空搜索
  onSearchClear() {
    this.setData({ searchValue: '' }, this._filterAndSetUsers);
  },

  // 提交搜索
  onSearchSubmit(e) {
    this.setData({ searchValue: e.detail.value }, this._filterAndSetUsers);
  },

  /**
   * _filterAndSetUsers: 过滤并设置用户列表的私有方法
   *
   * 功能说明：
   * 根据搜索关键词过滤用户列表，更新显示数据
   * 这是一个内部方法，用于统一处理数据筛选逻辑
   *
   * 筛选逻辑：
   * 1. 从原始用户列表开始筛选
   * 2. 根据搜索关键词进行模糊匹配
   * 3. 更新页面显示的筛选结果
   *
   * 搜索范围：
   * - 用户昵称（nickName）
   * - 用户openid（用于精确查找）
   */
  _filterAndSetUsers() {
    // 获取原始用户列表：作为筛选的数据源
    let filtered = this.data.userList;

    /**
     * 搜索关键词筛选
     *
     * 筛选条件：搜索框有输入内容时进行筛选
     * 搜索方式：模糊匹配，不区分大小写
     */
    if (this.data.searchValue) {
      // 去除首尾空格：避免空格影响搜索结果
      const val = this.data.searchValue.trim();

      // 过滤用户列表：使用Array.filter()方法
      filtered = filtered.filter(user => {
        /**
         * 多字段搜索逻辑
         *
         * 搜索字段：
         * 1. nickName：用户昵称，主要搜索字段
         * 2. openid：用户唯一标识，用于精确查找
         *
         * 匹配方式：
         * 使用indexOf()进行子字符串匹配
         * 返回-1表示不匹配，其他值表示匹配
         *
         * 逻辑关系：
         * 使用||（逻辑或），任一字段匹配即可
         */
        return (
          // 昵称匹配：检查用户昵称是否包含搜索关键词
          (user.nickName && user.nickName.indexOf(val) !== -1) ||

          // openid匹配：检查用户openid是否包含搜索关键词
          (user.openid && user.openid.indexOf(val) !== -1)
        );
      });
    }

    // 更新筛选结果：触发页面重新渲染
    this.setData({ filteredUserList: filtered });
  }
});

/**
 * 文件总结：user-management.js
 *
 * 这个文件实现了一个功能完整的用户管理系统，是健身房管理后台的核心模块。
 *
 * 主要特点：
 *
 * 1. 完整的用户管理功能：
 *    - 用户列表展示：分页显示所有用户信息
 *    - 角色管理：支持学员、讲师、管理员三种角色
 *    - 权限控制：基于角色的访问控制（RBAC）
 *    - 用户搜索：按姓名和openid搜索用户
 *    - 用户删除：安全的用户删除机制
 *    - 讲师信息管理：专门的讲师信息维护功能
 *
 * 2. 安全机制设计：
 *    - 权限验证：严格的管理员权限检查
 *    - 删除保护：多重确认机制防止误删
 *    - 倒计时机制：强制思考时间，避免冲动操作
 *    - 云函数调用：服务端权限验证和数据保护
 *    - 操作日志：重要操作的记录和追踪
 *
 * 3. 用户体验优化：
 *    - 分页加载：避免一次性加载大量数据
 *    - 实时搜索：输入时立即筛选结果
 *    - 加载状态：完整的loading状态管理
 *    - 错误处理：友好的错误提示和重试机制
 *    - 弹窗编辑：减少页面跳转，提升操作效率
 *
 * 4. 数据管理策略：
 *    - 双层数据结构：原始数据+筛选数据
 *    - 实时筛选：搜索和Tab切换的即时响应
 *    - 状态同步：操作后自动刷新相关数据
 *    - 内存优化：合理的数据加载和清理策略
 *
 * 5. 技术架构特点：
 *    - 模块化设计：功能拆分为独立的方法
 *    - 异步处理：大量使用async/await
 *    - 错误边界：完善的try-catch错误处理
 *    - 组件化UI：使用TDesign组件库
 *    - 云开发集成：云函数+云数据库
 *
 * 业务价值：
 *
 * 1. 运营管理：
 *    - 用户信息的集中管理和维护
 *    - 角色权限的灵活分配和调整
 *    - 讲师资源的专业化管理
 *    - 用户行为的监控和分析
 *
 * 2. 安全保障：
 *    - 严格的权限控制机制
 *    - 安全的用户数据操作
 *    - 完整的操作审计功能
 *    - 数据安全和隐私保护
 *
 * 3. 效率提升：
 *    - 批量用户管理操作
 *    - 快速的用户查找功能
 *    - 便捷的角色分配流程
 *    - 自动化的数据同步机制
 *
 * 与您熟悉的技术对比：
 *
 * - 权限系统：类似于ASP.NET的角色权限管理
 * - 数据管理：类似于Entity Framework的数据操作
 * - 用户界面：类似于WinForms的管理界面设计
 * - 异步处理：类似于C#的async/await模式
 * - 分页机制：类似于ASP.NET的分页控件
 *
 * 设计模式应用：
 *
 * 1. 观察者模式：数据变化自动更新UI
 * 2. 策略模式：不同角色的不同处理策略
 * 3. 工厂模式：用户对象的创建和管理
 * 4. 命令模式：用户操作的封装和执行
 * 5. 状态模式：不同状态下的不同行为
 *
 * 学习价值：
 *
 * 这个文件展示了企业级用户管理系统的完整实现：
 * 1. 复杂业务逻辑的前端实现方法
 * 2. 安全机制在前端的具体应用
 * 3. 用户体验优化的实践技巧
 * 4. 大数据量管理的性能优化策略
 * 5. 前后端协作的最佳实践
 *
 * 对于有后端开发经验的开发者来说，这个文件很好地展示了
 * 如何在前端实现传统后台管理系统的复杂功能。
 */