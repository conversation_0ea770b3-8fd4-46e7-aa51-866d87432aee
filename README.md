# 伽House小程序

<div align="center">
  <img src="https://img.shields.io/badge/微信小程序-2.20.1+-green.svg" alt="微信小程序版本">
  <img src="https://img.shields.io/badge/云开发-支持-blue.svg" alt="云开发支持">
  <img src="https://img.shields.io/badge/TDesign-UI组件库-orange.svg" alt="TDesign组件库">
  <img src="https://img.shields.io/badge/状态-生产环境-success.svg" alt="项目状态">
</div>

## 📖 项目简介

伽House是一个功能完整的工作室管理系统，基于微信小程序云开发构建。系统支持多角色权限管理，提供完整的课程预约、会员卡管理、讲师排课等功能，适用于瑜伽工作室、健身房等场所的数字化管理。

### ✨ 核心特色
- 🎯 **多角色权限系统** - 学员、讲师、管理员三级权限管理
- 💳 **会员卡系统** - 支持次卡、期卡等多种会员卡类型
- 📅 **智能排课系统** - 讲师可视化排课，学员便捷预约
- 📊 **数据统计分析** - 预约数据、收入统计等管理报表
- 🔄 **实时同步** - 基于云开发的实时数据同步
- 📱 **响应式设计** - 适配各种屏幕尺寸的移动设备

## 🛠 技术架构

### 前端技术栈
- **微信小程序** - 原生小程序开发框架
- **TDesign** - 腾讯官方小程序UI组件库
- **JavaScript ES6+** - 现代JavaScript语法
- **WXSS** - 小程序样式语言

### 后端技术栈
- **微信云开发** - Serverless后端服务
- **云函数** - Node.js运行时
- **云数据库** - NoSQL文档数据库
- **云存储** - 文件存储服务

### 开发环境
- **环境ID**: `cloud1-1gm190n779af8083`
- **小程序AppID**: `wxf4efc4e381cce5e7`
- **基础库版本**: 2.20.1+

## 👥 用户角色与权限

### 🎓 学员 (Student)
- ✅ 浏览课程信息和讲师介绍
- ✅ 预约和取消课程
- ✅ 查看个人预约历史
- ✅ 管理个人会员卡
- ✅ 查看课程详情和预约状态

### 🧘‍♀️ 讲师 (Instructor)
- ✅ 查看个人课程安排
- ✅ 管理课程学员名单
- ✅ 查看学员预约信息
- ✅ 更新个人资料和介绍

### 👨‍💼 管理员 (Admin)
- ✅ 用户管理（学员、讲师权限分配）
- ✅ 课程管理（创建、编辑、上下线）
- ✅ 会员卡管理（发放、充值、统计）
- ✅ 数据统计和报表查看
- ✅ 系统设置和配置管理

## 📱 功能模块

### 🏠 首页模块 (`pages/index/`)
- 课程推荐和热门课程展示
- 快速预约入口
- 公告和活动信息

### 👨‍🏫 讲师模块 (`pages/coaches/`)
- 讲师列表和详细介绍
- 讲师专业资质展示
- 讲师课程安排查看

### 📅 课表模块 (`pages/schedule/`)
- 可视化课程表展示
- 多维度筛选（时间、讲师、课程类型）
- 一键预约功能

### 📋 课程详情 (`pages/course-detail/`)
- 详细课程信息展示
- 实时预约状态更新
- 学员评价和反馈

### 📊 课程管理 (`pages/course-management/`)
- 课程CRUD操作
- 批量管理功能
- 课程模板系统

### 📖 我的预约 (`pages/my-bookings/`)
- 预约记录管理
- 多状态筛选（已预约、已完成、已取消）
- 预约历史查询

### 👤 个人中心 (`pages/profile/`)
- 个人信息管理
- 会员卡状态查看
- 系统设置

## 🗄 数据库设计

### 核心数据集合

#### 👤 用户表 (`users`)
```javascript
{
  _id: "用户唯一标识",
  openid: "微信用户标识",
  nickName: "用户昵称",
  avatarUrl: "头像URL",
  roles: ["学员", "讲师", "管理员"], // 用户角色数组
  phone: "联系电话",
  createTime: "创建时间",
  updateTime: "更新时间"
}
```

#### 🏃‍♀️ 课程表 (`courses`)
```javascript
{
  _id: "课程唯一标识",
  name: "课程名称",
  coach: ["讲师openid数组"], // 业务逻辑应通过此字段查询users表获取教练信息
  coachName: ["教练名字数组"], // 仅供开发维护使用，业务逻辑严禁直接使用
  startTime: "开始时间",
  endTime: "结束时间",
  duration: "课程时长(分钟)",
  venue: "上课地点",
  capacity: "课程容量",
  status: "online|offline", // 课程状态
  activityDetail: "课程详情",
  images: ["课程图片fileID数组"], // 课程图片
  createTime: "创建时间",
  updateTime: "更新时间"
}
```

#### 📅 预约表 (`bookings`)
```javascript
{
  _id: "预约唯一标识",
  userId: "用户openid",
  courseId: "课程ID",
  cardNumber: "使用的会员卡号",
  status: "upcoming|completed|cancelled", // 预约状态
  createTime: "预约时间",
  updateTime: "更新时间"
}
```

#### 💳 会员卡表 (`membershipCard`)
```javascript
{
  _id: "会员卡唯一标识",
  cardNumber: "卡号",
  userId: "绑定用户openid",
  totalTimes: "总次数",
  remainingTimes: "剩余次数",
  validFrom: "有效期开始",
  validTo: "有效期结束",
  status: "正常|已用完|已过期"
}
```

## ☁️ 云函数架构

### 📋 管理类云函数 (`adminManagement`)
- `getCourseListPaged` - 分页获取课程列表
- `addCourse` / `updateCourse` - 课程增删改
- `updateCourseStatus` - 课程上下线
- `getUserList` - 用户管理
- `getMembershipCards` - 会员卡管理

### 📖 预约类云函数 (`bookingManagement`)
- `bookCourse` - 预约课程
- `cancelBooking` - 取消预约
- `getUserBookings` - 获取用户预约
- `getScheduleData` - 获取课表数据

### 👤 用户类云函数 (`userManagement`)
- `updateUserProfile` - 更新用户资料
- `getUserInfo` - 获取用户信息
- `updateUserRoles` - 权限管理

## 🚀 快速开始

### 环境要求
- **微信开发者工具** 1.05.0+
- **Node.js** 14.0+
- **微信小程序基础库** 2.20.1+

### 1️⃣ 项目初始化
```bash
# 克隆项目
git clone [项目地址]
cd miniprogram-6

# 安装小程序依赖
cd miniprogram
npm install

# 安装TDesign组件库
npm install tdesign-miniprogram --save --production

# 绕过限值安装TDesign依赖
cmd /c npm i tdesign-miniprogram -S --production --%
```

### 2️⃣ 云函数部署
```bash
# 部署所有云函数
cd cloudfunctions

# 部署管理类云函数
cd adminManagement && npm install
# 在微信开发者工具中右键部署

# 部署预约类云函数
cd ../bookingManagement && npm install
# 在微信开发者工具中右键部署

# 部署用户类云函数
cd ../userManagement && npm install
# 在微信开发者工具中右键部署
```

### 3️⃣ 数据库初始化
在微信开发者工具的云开发控制台中创建以下集合：
- ✅ `users` - 用户信息
- ✅ `courses` - 课程信息
- ✅ `bookings` - 预约记录
- ✅ `membershipCard` - 会员卡信息
- ✅ `membershipCardTemplate` - 会员卡模板
- ✅ `coursesTemplate` - 课程模板
- ✅ `systemSettings` - 系统配置

### 4️⃣ 权限配置
在云开发控制台设置数据库权限：
```javascript
// 建议的安全规则示例
{
  "read": true,
  "write": "auth.openid == resource.userId" // 用户只能操作自己的数据
}
```

## 🔧 开发指南

### 本地开发
1. 使用微信开发者工具打开项目根目录
2. 确保云开发环境ID配置正确
3. 编译并在模拟器中预览
4. 使用真机调试测试完整功能

### 代码规范
- **命名规范**: 使用驼峰命名法
- **注释规范**: 关键业务逻辑必须添加注释
- **错误处理**: 所有异步操作必须包含错误处理
- **数据验证**: 用户输入数据必须进行验证

### 调试技巧
- 📊 **控制台调试**: 使用`console.log`输出关键变量
- 🔍 **网络调试**: 在开发者工具中查看云函数调用
- 💾 **数据库调试**: 在云开发控制台查看数据变化
- 📱 **真机调试**: 使用真机调试测试微信相关功能

## 🔒 安全考虑

### 数据安全
- ✅ 用户数据加密存储
- ✅ 敏感信息脱敏处理
- ✅ 数据库访问权限控制
- ✅ 云函数参数验证

### 业务安全
- ✅ 预约时间冲突检测
- ✅ 会员卡余额验证
- ✅ 重复预约防护
- ✅ 恶意操作限制

## 📈 性能优化

### 前端优化
- 🚀 **懒加载**: 图片和数据按需加载
- 💾 **缓存策略**: 合理使用本地缓存
- 🔄 **数据同步**: 增量更新减少网络请求
- 📱 **体验优化**: 加载状态和错误提示

### 后端优化
- 📊 **数据库索引**: 关键字段建立索引
- 🔍 **查询优化**: 减少不必要的数据查询
- ⚡ **云函数优化**: 减少冷启动时间
- 📈 **并发处理**: 合理处理高并发场景

## 🐛 常见问题

### Q: 云函数调用失败？
**A**: 检查云函数是否正确部署，环境ID是否匹配，参数格式是否正确。

### Q: 数据库权限错误？
**A**: 确认数据库安全规则配置，检查用户是否有相应操作权限。

### Q: 页面显示异常？
**A**: 检查数据格式是否正确，网络请求是否成功，错误处理是否完善。

### Q: 预约功能异常？
**A**: 确认会员卡余额，检查课程状态，验证时间冲突逻辑。

## 📞 技术支持

如遇到技术问题，请按以下方式获取帮助：

1. 📖 查看项目文档和代码注释
2. 🔍 搜索相关技术文档
3. 💬 联系开发团队获取支持

---

<div align="center">
  <p>💝 感谢使用伽House小程序系统</p>
  <p>🌟 如果这个项目对你有帮助，请给我们一个Star！</p>
</div>

