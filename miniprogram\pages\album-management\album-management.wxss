/* miniprogram/pages/album-management/album-management.wxss */
page {
  height: 100%;
  background-color: #f7f7f7;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 空状态 */
.empty-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-bottom: 100rpx;
}

.empty-text {
  color: #888;
  font-size: 28rpx;
  margin-top: 30rpx;
}

/* 照片滚动和网格 */
.photo-scroll-view {
  flex: 1;
  height: 100%;
  padding-bottom: 140rpx; /* 为底部操作栏留出空间 */
}

.date-header {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  padding: 40rpx 24rpx 20rpx;
}

.photo-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 4rpx;
  padding: 0 4rpx;
}

.photo-item-wrapper {
  position: relative;
  width: 100%;
  padding-bottom: 100%; /* 保持 1:1 的宽高比 */
  height: 0;
}

.photo-item {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.photo-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 选中效果 */
.selection-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  border: 2px solid #07c160;
  box-sizing: border-box;
}

.checkmark {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #07c160;
  color: white;
  position: relative;
}

.checkmark::after {
  content: '';
  position: absolute;
  left: 12rpx;
  top: 6rpx;
  width: 10rpx;
  height: 18rpx;
  border: solid white;
  border-width: 0 4rpx 4rpx 0;
  transform: rotate(45deg);
}

/* 角标 */
.order-badge, .homepage-badge {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20rpx;
  font-weight: bold;
}

.order-badge {
  background-color: #07c160;
  width: 36rpx;
  height: 36rpx;
}

.homepage-badge {
  background-color: rgba(0, 0, 0, 0.3);
  width: 32rpx;
  height: 32rpx;
  padding: 4rpx; /* 给 t-icon 一点空间 */
}

.homepage-star-icon {
  /* t-icon 继承了父元素的 display:flex, 这里可以不用额外样式 */
}

/* 浮动上传按钮 */
.fab-container {
  position: fixed;
  right: 40rpx;
  bottom: 180rpx;
  z-index: 10;
}

.fab {
  width: 110rpx;
  height: 110rpx;
  border-radius: 50%;
  background-color: #07c160;
  color: white;
  display: flex; /* 使用 flex 居中 t-icon */
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  border: none;
  padding: 0; /* 重置 button 默认 padding */
}

/* 底部操作栏 */
.action-bar-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 20rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.08);
  z-index: 20;
}

.action-bar {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.action-button {
  flex: 1;
  margin: 0 10rpx;
  font-size: 28rpx;
  padding: 15rpx 0;
  border-radius: 8rpx;
  background-color: #f0f0f0;
  color: #333;
}

.action-button.primary {
  background-color: #07c160;
  color: white;
}

.action-button.secondary {
  background-color: #e6e6e6;
  color: #555;
}
