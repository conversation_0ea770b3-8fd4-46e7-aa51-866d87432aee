/**
 * 相册管理页面样式
 *
 * 设计说明：
 * 采用现代化的网格布局，参考course-edit页面的图片管理设计
 * 优先使用TDesign组件，保持设计一致性
 */

/**
 * 页面容器样式
 */
.album-management-container {
  min-height: 100vh;
  padding: 32rpx;
  background-color: #f8f9fa;
  box-sizing: border-box;
}

/**
 * 操作按钮区域样式
 *
 * 设计说明：
 * 与下方图片容器保持相同的左右边距
 * 确保按钮不会超出屏幕范围
 */
.album-header {
  position: fixed;
  top: 0;
  left: 32rpx;
  right: 32rpx;
  z-index: 100;
  background: #fff;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  padding: 24rpx 0 16rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  overflow-x: auto;
  box-sizing: border-box;
  border-radius: 0 0 12rpx 12rpx;
}

/**
 * 操作按钮样式
 *
 * 设计说明：
 * 按照TDesign设计规范设置按钮样式
 * 确保按钮宽度足够显示完整文字
 */
.action-btn {
  border-radius: 6rpx !important;
  font-weight: 400 !important;
  box-shadow: none !important;
  padding: 0 24rpx !important;
  min-width: 160rpx;
  max-width: 200rpx;
  white-space: nowrap;
  flex-shrink: 0;
  font-size: 26rpx !important;
  height: 64rpx !important;
}

/**
 * 默认按钮样式（选择首页图片）
 */
.action-btn[theme="default"] {
  background: #f3f3f3 !important;
  color: #666 !important;
  border: 1rpx solid #d9d9d9 !important;
}

/**
 * 主要按钮样式（完成选择）
 */
.action-btn[theme="primary"] {
  background: #0052d9 !important;
  color: #fff !important;
  border: 1rpx solid #0052d9 !important;
}

/**
 * 危险按钮样式（删除模式）
 */
.action-btn[theme="danger"] {
  background: #e34d59 !important;
  color: #fff !important;
  border: 1rpx solid #e34d59 !important;
}

/**
 * 图片网格容器样式
 *
 * 设计说明：
 * 使用CSS Grid布局创建响应式的图片网格
 * 参考course-edit页面的image-grid样式
 * 与头部容器保持相同的左右边距
 */
.album-grid {
  margin-top: 120rpx; /* 为固定头部留出空间 */
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  width: 100%;
}

/**
 * 图片项容器样式
 *
 * 设计说明：
 * 每个图片项的容器，包含图片和各种操作按钮
 * 使用相对定位为子元素提供定位基准
 */
.album-item {
  position: relative;
  width: 100%;
  aspect-ratio: 1; /* 保持1:1的宽高比 */
  border-radius: 8rpx;
  overflow: hidden;
  background-color: #f5f5f5;
}

/**
 * 相册图片样式
 */
.album-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

/**
 * 上传按钮项样式
 *
 * 设计说明：
 * 参考course-edit页面的image-add-btn样式
 * 使用虚线边框和柔和的颜色
 */
.upload-item {
  border: 2rpx dashed #ddd;
  background-color: #fafafa;
  display: flex;
  align-items: center;
  justify-content: center;
}

/**
 * 上传按钮容器样式
 */
.upload-btn-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

/**
 * 上传按钮文字样式
 */
.upload-text {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

/**
 * 上传状态提示样式
 */
.upload-status {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
}

/**
 * 上传中文字样式
 */
.uploading-text {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #666;
}



/**
 * 首页编号徽章样式
 *
 * 设计说明：
 * 显示在图片左上角的编号徽章
 * 使用品牌绿色，突出显示首页图片
 */
.banner-order-badge {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  z-index: 15;
  background: #52c41a;
  color: white;
  border-radius: 50%;
  width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

/**
 * 删除模式复选框样式
 */
.delete-checkbox {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  z-index: 15;
  transform: scale(1.2);
}

.album-management-container {
  min-height: 100vh; /* 保证内容区至少和屏幕一样高 */
  height: auto;      /* 允许内容自动撑开 */
  overflow: visible; /* 保证内容不会被裁剪 */
  padding-left: 32rpx;
  padding-right: 32rpx;
  padding-top: 0;
  padding-bottom: 0;
  box-sizing: border-box;
} 

/**
 * 警告提示样式
 *
 * 设计说明：
 * 简洁的文字提示，无背景
 * 图标和文字在同一行显示
 */
.album-warning {
  margin-top: 40rpx;
  padding: 24rpx 0;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  text-align: center;
}

/**
 * 高亮文字样式
 */
.album-warning .highlight {
  color: #fa541c;
  font-weight: 600;
}

.delete-checkbox {
  /* 让复选框绝对定位在图片右上角，类似HTML的position: absolute; */
  position: absolute;
  right: 12rpx; /* 距离右边12rpx */
  top: 12rpx;  /* 距离顶部12rpx */
  z-index: 10; /* 保证在图片之上 */
  /* 缩小复选框，提升美观。类似CSS的transform: scale(1.1); */
  transform: scale(1.1);
  /* 可选：为兼容小程序不同机型，建议加-webkit-transform */
  -webkit-transform: scale(1.1);
}

/**
 * 加载状态项样式
 */
.loading-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  border: 1rpx solid #e5e5e5;
  border-radius: 8rpx;
  color: #999;
}

/**
 * 加载文字样式
 */
.loading-text {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #999;
}

/**
 * 结束提示项样式
 *
 * 设计说明：
 * 占用一张图片的位置，与其他图片保持一致的布局
 */
.end-item {
  background-color: #fafafa;
  border: 1rpx solid #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: center;
}

/**
 * 结束提示内容样式
 */
.end-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

/**
 * 结束提示文字样式
 */
.end-text {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #999;
}

/**
 * 响应式设计
 */
@media screen and (max-width: 750rpx) {
  .album-header {
    left: 24rpx;
    right: 24rpx;
    gap: 12rpx;
  }

  .action-btn {
    padding: 0 20rpx !important;
    min-width: 140rpx;
    max-width: 180rpx;
    font-size: 24rpx !important;
    height: 60rpx !important;
  }

  .album-grid {
    gap: 12rpx;
    margin-top: 110rpx;
  }

  .upload-text {
    font-size: 22rpx;
  }

  .loading-text {
    font-size: 22rpx;
  }

  .end-text {
    font-size: 22rpx;
  }

  .album-warning {
    font-size: 24rpx;
    padding: 20rpx 0;
  }
}

