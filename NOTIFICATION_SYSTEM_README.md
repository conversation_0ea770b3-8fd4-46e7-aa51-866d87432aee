# 通知系统部署指南

## 概述

本通知系统为微信小程序项目添加了完整的消息通知功能，包括即时通知和定时提醒。

## 功能特性

### 1. 即时触发通知
- **预约成功**：学员和讲师都会收到通知
- **学员取消预约**：学员和讲师都会收到通知  
- **管理员取消预约**：学员和讲师都会收到通知
- **课程下线**：讲师和已预约学员都会收到通知

### 2. 定时触发通知
- **每日提醒**：每天晚上20:00提醒明天有课程的用户
- **课程开始前提醒**：根据系统设置提醒即将开始的课程（默认3小时前）
- **数据清理**：每天凌晨2:00清理30天前的过期通知

### 3. 前端功能
- **通知列表页面**：支持分页加载、已读未读筛选
- **通知入口**：个人中心显示未读数量红点
- **批量操作**：全部标记已读功能

## 部署步骤

### 1. 云函数部署

#### 1.1 部署通知管理云函数
```bash
cd cloudfunctions/notificationManagement
npm install
```
在微信开发者工具中右键该文件夹，选择"上传并部署：云端安装依赖"

#### 1.2 部署通知调度云函数
```bash
cd cloudfunctions/notificationScheduler  
npm install
```
在微信开发者工具中右键该文件夹，选择"上传并部署：云端安装依赖"

### 2. 数据库集合创建

在微信云开发控制台创建以下集合：

#### 2.1 notifications 集合
```javascript
{
  _id: "通知ID",
  recipientId: "接收者openid", 
  type: "通知类型",
  title: "通知标题",
  content: "通知内容", 
  courseId: "相关课程ID",
  bookingId: "相关预约ID",
  isRead: false,
  createTime: new Date(),
  updateTime: new Date(),
  expireTime: new Date()
}
```

#### 2.2 数据库索引配置
为提高查询性能，建议创建以下索引：

**notifications 集合索引：**
- `recipientId_1_createTime_-1`：按接收者和创建时间查询
- `recipientId_1_isRead_1`：按接收者和已读状态查询
- `expireTime_1`：按过期时间查询（用于数据清理）

### 3. 定时触发器配置

#### 3.1 自动配置（推荐）
云函数部署时会自动读取 `config.json` 文件创建触发器。

#### 3.2 手动配置
如果自动配置失败，可在云开发控制台手动创建：

1. 进入云开发控制台 → 云函数 → notificationScheduler
2. 点击"触发器"选项卡
3. 添加以下触发器：

**每日提醒触发器：**
- 触发器名称：dailyReminder
- 触发方式：定时触发
- Cron表达式：`0 0 20 * * * *`（每天20:00执行）

**课程提醒触发器：**
- 触发器名称：courseReminder  
- 触发方式：定时触发
- Cron表达式：`0 0 * * * * *`（每小时执行）

**数据清理触发器：**
- 触发器名称：cleanExpiredNotifications
- 触发方式：定时触发  
- Cron表达式：`0 0 2 * * * *`（每天凌晨2:00执行）

### 4. 系统设置配置

在 systemSettings 集合的 system_settings 文档中添加通知相关配置：

```javascript
{
  notification: {
    reminderHours: 3,        // 课程提醒时间（小时）
    smsEnabled: false,       // 短信通知开关（预留）
    emailEnabled: false      // 邮件通知开关（预留）
  }
}
```

## 测试验证

### 1. 功能测试

#### 1.1 即时通知测试
1. 学员预约课程 → 检查学员和讲师是否收到通知
2. 学员取消预约 → 检查学员和讲师是否收到通知
3. 管理员取消预约 → 检查学员和讲师是否收到通知
4. 管理员下线课程 → 检查讲师和已预约学员是否收到通知

#### 1.2 定时通知测试
1. 创建明天的课程并预约 → 检查今晚20:00是否收到每日提醒
2. 创建3小时后的课程并预约 → 检查是否收到课程开始前提醒

#### 1.3 前端功能测试
1. 个人中心是否显示未读数量红点
2. 通知列表页面是否正常显示
3. 筛选功能是否正常工作
4. 标记已读功能是否正常工作

### 2. 性能测试

#### 2.1 数据量测试
- 创建大量通知数据，测试分页加载性能
- 测试批量标记已读的性能

#### 2.2 并发测试  
- 同时多个用户操作，测试通知发送的稳定性

## 注意事项

### 1. 权限配置
确保云函数有足够的权限访问数据库集合。

### 2. 错误处理
- 通知发送失败不会影响主业务流程
- 定时任务执行失败会记录日志但不会中断服务

### 3. 性能优化
- 通知列表支持分页加载，避免一次性加载过多数据
- 过期通知会自动清理，避免数据库膨胀
- 合理使用数据库索引提高查询性能

### 4. 扩展性
- 预留了短信和邮件通知接口
- 支持添加新的通知类型
- 支持富文本通知内容扩展

## 故障排除

### 1. 通知不发送
- 检查云函数是否正常部署
- 检查云函数调用日志
- 检查数据库权限配置

### 2. 定时任务不执行
- 检查触发器是否正确配置
- 检查Cron表达式是否正确
- 检查云函数执行日志

### 3. 前端显示异常
- 检查页面路由是否正确配置
- 检查组件引用是否正确
- 检查样式文件是否正确加载

## 联系支持

如遇到问题，请检查：
1. 云函数执行日志
2. 数据库操作日志  
3. 前端控制台错误信息

提供详细的错误信息有助于快速定位和解决问题。
