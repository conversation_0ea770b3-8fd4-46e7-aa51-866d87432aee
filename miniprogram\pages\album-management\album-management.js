// 获取全局 app 实例（如需用到全局数据可用）
const app = getApp();

// 引入云开发能力
const db = wx.cloud.database(); // 获取数据库实例
import { showToast, showLoading, hideToast, showError } from '../../utils/toast.js';

Page({
  data: {
    albumImages: [], // 存储相册图片列表（每项包含 _id、fileID、tempFileURL、bannerOrder、selected）
    maxBannerCount: 5, // 首页最多展示5张图片
    chooseBannerMode: false, // 是否激活首页图片选择模式
    deleteMode: false, // 是否激活批量删除模式
    page: 0, // 当前页码，从0开始
    pageSize: 20, // 每页加载20张图片
    hasMore: true, // 是否还有更多图片可加载
    loading: false, // 是否正在加载中
    isUploading: false // 是否正在上传图片
  },

  // 页面加载时自动获取第一页图片
  onLoad: function() {
    this.loadAlbumImages(true); // true表示重置分页
  },

  // 分页加载图片
  loadAlbumImages: function(reset = false) {
    if (this.data.loading || (!this.data.hasMore && !reset)) return; // 正在加载或没有更多时不再请求
    this.setData({ loading: true });
    let page = reset ? 0 : this.data.page;
    if (reset) {
      this.setData({ albumImages: [], hasMore: true, page: 0 });
    }
    db.collection('album_images')
      .orderBy('createTime', 'desc')
      .skip(page * this.data.pageSize)
      .limit(this.data.pageSize)
      .get()
      .then(res => {
        const fileList = res.data.map(item => item.fileID);
        if (fileList.length === 0) {
          this.setData({ hasMore: false, loading: false });
          return;
        }
        wx.cloud.getTempFileURL({
          fileList: fileList,
          success: urlRes => {
            const urlMap = {};
            urlRes.fileList.forEach(file => {
              urlMap[file.fileID] = file.tempFileURL;
            });
            const newImages = res.data.map(item => ({
              ...item,
              tempFileURL: urlMap[item.fileID] || '',
              bannerOrder: item.bannerOrder || null
            }));
            const albumImages = reset ? newImages : this.data.albumImages.concat(newImages);
            this.setData({
              albumImages,
              hasMore: newImages.length === this.data.pageSize, // 如果本页数量等于pageSize，说明可能还有下一页
              page: page + 1,
              loading: false
            });
          },
          fail: () => {
            this.setData({ loading: false });
          }
        });
      });
  },

  // 页面滚动到底部时自动加载下一页
  onReachBottom: function() {
    this.loadAlbumImages();
  },

  // 获取当前用户的相册图片列表
  getAlbumImages: function() {
    db.collection('album_images')
      .orderBy('createTime', 'desc')
      .limit(100) // 明确指定最多拉取100条，防止只显示部分图片
      .get()
      .then(res => {
        const fileList = res.data.map(item => item.fileID);
        if (fileList.length === 0) {
          this.setData({ albumImages: [] });
          return;
        }
        wx.cloud.getTempFileURL({
          fileList: fileList,
          success: urlRes => {
            const urlMap = {};
            urlRes.fileList.forEach(file => {
              urlMap[file.fileID] = file.tempFileURL;
            });
            // 组装 albumImages 数组，带 bannerOrder 字段
            const albumImages = res.data.map(item => ({
              ...item,
              tempFileURL: urlMap[item.fileID] || '',
              bannerOrder: item.bannerOrder || null // 可能为 undefined/null/数字
            }));
            this.setData({ albumImages });
          },
          fail: err => {
            showToast(this, { message: '获取图片失败', theme: 'error' });
          }
        });
      });
  },

  // 激活首页图片选择模式
  onChooseBannerMode: function() {
    this.setData({ chooseBannerMode: true });
  },

  // 退出首页图片选择模式
  onCancelChooseBanner: function() {
    this.setData({ chooseBannerMode: false });
  },

  // 激活删除模式
  onDeleteMode: function() {
    // 进入批量删除模式时，为每张图片加selected字段，初始为false
    let albumImages = this.data.albumImages.map(img => ({ ...img, selected: false }));
    this.setData({ deleteMode: true, albumImages });
  },

  // 选择/取消选中图片（批量删除模式下）
  onSelectImage: function(e) {
    // 获取当前图片索引
    const index = e.currentTarget.dataset.index;
    let albumImages = this.data.albumImages;
    // 切换选中状态
    albumImages[index].selected = !albumImages[index].selected;
    this.setData({ albumImages });
  },

  // 退出批量删除模式并批量删除选中图片
  onCancelDeleteMode: function() {
    // 获取所有被选中的图片id和fileID
    const selected = this.data.albumImages.filter(img => img.selected);
    if (selected.length === 0) {
      // 没有选中任何图片，直接退出批量删除模式
      this.setData({ deleteMode: false });
      return;
    }
    // 弹窗确认
    wx.showModal({
      title: '批量删除',
      content: `确定要删除选中的${selected.length}张图片吗？`,
      confirmText: '删除',
      confirmColor: '#E34D59',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 1. 批量删除数据库记录
          const db = wx.cloud.database();
          const batchRemove = selected.map(img => db.collection('album_images').doc(img._id).remove());
          Promise.all(batchRemove).then(() => {
            // 2. 批量删除云存储文件
            const fileList = selected.map(img => img.fileID);
            wx.cloud.deleteFile({
              fileList,
              success: () => {
                showToast(this, { message: '删除成功', theme: 'success' });
                this.getAlbumImages(); // 刷新图片列表
                this.setData({ deleteMode: false });
              },
              fail: () => {
                showError(this, '云存储删除失败');
                this.setData({ deleteMode: false });
              }
            });
          }).catch(() => {
            showError(this, '数据库删除失败');
            this.setData({ deleteMode: false });
          });
        } else {
          // 用户取消，退出批量删除模式
          this.setData({ deleteMode: false });
        }
      }
    });
  },

  // 选择/取消首页图片（仅在选择模式下可用）
  onToggleBanner: function(e) {
    if (!this.data.chooseBannerMode) {
      // 不是选择模式，转为预览图片
      this.onPreviewImage(e);
      return;
    }
    const index = e.currentTarget.dataset.index;
    let { albumImages, maxBannerCount } = this.data;
    let img = albumImages[index];
    const selected = albumImages.filter(i => i.bannerOrder).sort((a, b) => a.bannerOrder - b.bannerOrder);
    if (img.bannerOrder) {
      const oldOrder = img.bannerOrder;
      img.bannerOrder = null;
      albumImages.forEach(i => {
        if (i.bannerOrder && i.bannerOrder > oldOrder) {
          i.bannerOrder--;
        }
      });
    } else {
      if (selected.length >= maxBannerCount) {
        showToast(this, { message: '最多只能选5张首页图片', theme: 'warning' });
        return;
      }
      img.bannerOrder = selected.length + 1;
    }
    showLoading(this, '保存中...');
    db.collection('album_images').doc(img._id).update({
      data: { bannerOrder: img.bannerOrder },
      success: () => {
        hideToast(this);
        this.getAlbumImages();
      },
      fail: () => {
        hideToast(this);
        showError(this, '保存失败');
      }
    });
  },

  // 上传图片
  onUploadImage: function() {
    // 防止重复上传
    if (this.data.isUploading) return;

    wx.chooseImage({
      count: 9, // 一次最多可选9张图片（小程序限制）
      sizeType: ['compressed'], // 使用压缩图片
      sourceType: ['album', 'camera'],
      success: chooseRes => {
        const filePaths = chooseRes.tempFilePaths; // 本地临时路径数组
        if (filePaths.length === 0) return;

        // 设置上传状态
        this.setData({ isUploading: true });

        // 用Promise.all批量上传所有图片
        const uploadTasks = filePaths.map(filePath => {
          // 生成云存储路径，index-images/ 文件夹下，文件名用时间戳+随机数
          const timestamp = Date.now();
          const randomStr = Math.random().toString(36).substr(2, 9);
          const cloudPath = `index-images/${timestamp}_${randomStr}.jpg`;

          return wx.cloud.uploadFile({
            cloudPath,
            filePath
          }).then(uploadRes => {
            // 上传成功后，将 fileID 存入数据库
            return db.collection('album_images').add({
              data: {
                fileID: uploadRes.fileID,
                createTime: new Date()
              }
            });
          });
        });

        // 全部上传完成后刷新图片列表
        Promise.all(uploadTasks).then(() => {
          showToast(this, { message: '上传成功', theme: 'success' });
          this.loadAlbumImages(true); // 重新加载图片列表
        }).catch(error => {
          console.error('图片上传失败:', error);
          showError(this, '有图片上传失败');
        }).finally(() => {
          // 重置上传状态
          this.setData({ isUploading: false });
        });
      },
      fail: () => {
        // 用户取消选择图片
        this.setData({ isUploading: false });
      }
    });
  },

  // 删除图片（带确认弹窗）
  onDeleteImage: function(e) {
    const id = e.currentTarget.dataset.id; // 数据库记录id
    const fileID = e.currentTarget.dataset.fileid; // 云存储fileID
    // 弹窗提示用户确认
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张图片吗？',
      confirmText: '删除',
      confirmColor: '#E34D59', // 红色
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 用户点击了“删除”
          // 1. 先删除数据库记录
          db.collection('album_images').doc(id).remove({
            success: () => {
              // 2. 再删除云存储文件
              wx.cloud.deleteFile({
                fileList: [fileID],
                success: () => {
                  showToast(this, { message: '删除成功', theme: 'success' });
                  this.getAlbumImages(); // 刷新图片列表
                },
                fail: () => {
                  showError(this, '云存储删除失败');
                }
              });
            },
            fail: () => {
              showError(this, '数据库删除失败');
            }
          });
        }
        // 用户点击“取消”则不做任何操作
      }
    });
  },

  // 预览图片（仅在非选择/删除模式下可用）
  onPreviewImage: function(e) {
    if (this.data.chooseBannerMode || this.data.deleteMode) return; // 选择或删除模式下不响应
    const index = e.currentTarget.dataset.index;
    const urls = this.data.albumImages.map(item => item.tempFileURL);
    wx.previewImage({
      current: urls[index],
      urls: urls
    });
  },


});