// miniprogram/pages/album-management/album-management.js
const app = getApp();

Page({
  data: {
    photoGroups: [], // 按日期分组的照片列表
    isAdmin: false,
    
    selectionMode: false, // 是否为选择模式
    isOrderingMode: false, // 是否为排序模式

    selectedPhotos: new Set(), // 记录选中的照片 fileID
    tempHomepageOrder: [], // 临时存储首页排序的 fileID 列表
    originalHomepageOrder: [], // 保存进入排序模式前的原始顺序
  },

  onLoad: function (options) {
    this.checkAdminStatus();
  },

  onShow: function() {
    // 每次进入页面都刷新数据，以保证数据最新
    if (this.data.isAdmin) {
      this.fetchAndGroupPhotos();
    }
  },

  // 1. 数据加载与处理
  async checkAdminStatus() {
    const isAdmin = app.globalData.isAdmin;
    this.setData({ isAdmin });
    if (!isAdmin) {
      wx.showToast({
        title: '非管理员无权访问',
        icon: 'error',
        duration: 2000,
        complete: () => wx.switchTab({ url: '/pages/index/index' })
      });
    }
  },

  async fetchAndGroupPhotos() {
    wx.showLoading({ title: '加载中...' });
    try {
      const [photosRes, homepagePhotosRes] = await Promise.all([
        wx.cloud.callFunction({ name: 'adminManagement', data: { action: 'getAlbumPhotos' } }),
        wx.cloud.callFunction({ name: 'userManagement', data: { action: 'getHomepagePhotos' } })
      ]);

      if (!photosRes.result || !homepagePhotosRes.result) {
        throw new Error('获取照片失败');
      }

      const allPhotos = photosRes.result.data || [];
      const homepagePhotos = homepagePhotosRes.result.data || [];
      
      const homepagePhotoMap = new Map(homepagePhotos.map(p => [p.fileID, p.homepageOrder]));
      this.setData({ 
        originalHomepageOrder: homepagePhotos.map(p => p.fileID)
      });

      allPhotos.forEach(photo => {
        photo.selected = false; // 用于多选
        photo.homepageOrder = homepagePhotoMap.get(photo.fileID) || 0; // 用于排序
      });

      const photoGroups = this.groupPhotosByDate(allPhotos);
      this.setData({ photoGroups });

    } catch (error) {
      console.error("获取照片数据失败", error);
      wx.showToast({ title: '数据加载失败', icon: 'error' });
    } finally {
      wx.hideLoading();
    }
  },

  groupPhotosByDate(photos) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);

    const groups = {};
    photos.sort((a, b) => new Date(b.createTime) - new Date(a.createTime)); // 按时间倒序

    photos.forEach(photo => {
      const photoDate = new Date(photo.createTime);
      let dateKey;

      const photoDateMidnight = new Date(photoDate);
      photoDateMidnight.setHours(0, 0, 0, 0);

      if (photoDateMidnight.getTime() === today.getTime()) {
        dateKey = '今天';
      } else if (photoDateMidnight.getTime() === yesterday.getTime()) {
        dateKey = '昨天';
      } else {
        dateKey = `${photoDate.getFullYear()}年${photoDate.getMonth() + 1}月${photoDate.getDate()}日`;
      }

      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(photo);
    });

    return Object.keys(groups).map(date => ({
      date,
      photos: groups[date]
    }));
  },

  // 2. 核心交互逻辑
  onImageTap(e) {
    const { fileid, groupindex, photoindex } = e.currentTarget.dataset;

    if (this.data.isOrderingMode) {
      this.handleOrderingClick(fileid);
    } else if (this.data.selectionMode) {
      this.handleSelectionClick(fileid, groupindex, photoindex);
    } else {
      this.previewImage(groupindex, photoindex);
    }
  },

  onImageLongPress(e) {
    if (this.data.isOrderingMode) return; // 排序模式下禁用长按
    const { fileid, groupindex, photoindex } = e.currentTarget.dataset;
    this.setData({ selectionMode: true });
    this.handleSelectionClick(fileid, groupindex, photoindex);
  },

  previewImage(currentGroupIndex, currentPhotoIndex) {
    const currentGroup = this.data.photoGroups[currentGroupIndex];
    const urls = currentGroup.photos.map(p => p.tempFileURL);
    const currentUrl = urls[currentPhotoIndex];
    wx.previewImage({ current: currentUrl, urls });
  },

  // 3. 选择模式相关
  handleSelectionClick(fileID, groupIndex, photoIndex) {
    const { selectedPhotos, photoGroups } = this.data;
    const photo = photoGroups[groupIndex].photos[photoIndex];
    const isSelected = selectedPhotos.has(fileID);

    if (isSelected) {
      selectedPhotos.delete(fileID);
    } else {
      selectedPhotos.add(fileID);
    }
    
    this.setData({
      [`photoGroups[${groupIndex}].photos[${photoIndex}].selected`]: !isSelected,
      selectedPhotos,
    });

    // 如果所有照片都取消选择了，自动退出选择模式
    if (selectedPhotos.size === 0) {
      this.setData({ selectionMode: false });
    }
  },

  cancelSelection() {
    const { photoGroups } = this.data;
    photoGroups.forEach((group, gIndex) => {
      group.photos.forEach((photo, pIndex) => {
        if (photo.selected) {
          this.setData({ [`photoGroups[${gIndex}].photos[${pIndex}].selected`]: false });
        }
      });
    });
    this.setData({
      selectionMode: false,
      selectedPhotos: new Set(),
    });
  },

  async handleDeleteSelected() {
    const fileIDs = Array.from(this.data.selectedPhotos);
    if (fileIDs.length === 0) return;

    wx.showModal({
      title: '确认删除',
      content: `确定要删除选中的 ${fileIDs.length} 张照片吗？`,
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({ title: '删除中...' });
          try {
            await wx.cloud.callFunction({
              name: 'adminManagement',
              data: {
                action: 'deleteAlbumPhoto',
                fileIDs: fileIDs,
              }
            });
            wx.showToast({ title: '删除成功', icon: 'success' });
            this.cancelSelection();
            this.fetchAndGroupPhotos();
          } catch (error) {
            wx.hideLoading();
            wx.showToast({ title: '删除失败', icon: 'error' });
            console.error("删除照片失败", error);
          }
        }
      }
    });
  },

  // 4. 排序模式相关
  enterOrderingMode() {
    const initialOrder = this.data.originalHomepageOrder.slice(); // 深拷贝
    this.setData({
      isOrderingMode: true,
      selectionMode: false, // 确保退出选择模式
      tempHomepageOrder: initialOrder,
    });
    this.updatePhotoOrderNumbers(initialOrder);
  },

  handleOrderingClick(fileID) {
    const { tempHomepageOrder } = this.data;
    const index = tempHomepageOrder.indexOf(fileID);

    if (index > -1) { // 如果已在排序列表中，则移除
      tempHomepageOrder.splice(index, 1);
    } else { // 否则，添加到列表末尾
      tempHomepageOrder.push(fileID);
    }
    
    this.updatePhotoOrderNumbers(tempHomepageOrder);
  },

  updatePhotoOrderNumbers(orderList) {
    const { photoGroups } = this.data;
    const orderMap = new Map(orderList.map((id, index) => [id, index + 1]));

    photoGroups.forEach((group, gIndex) => {
      group.photos.forEach((photo, pIndex) => {
        const newOrder = orderMap.get(photo.fileID) || 0;
        if (photo.homepageOrder !== newOrder) {
          this.setData({
            [`photoGroups[${gIndex}].photos[${pIndex}].homepageOrder`]: newOrder
          });
        }
      });
    });
    this.setData({ tempHomepageOrder: orderList });
  },

  async saveHomepageOrder() {
    wx.showLoading({ title: '保存中...' });
    try {
      await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'setHomepagePhotosOrder',
          orderedFileIDs: this.data.tempHomepageOrder,
        }
      });
      wx.showToast({ title: '保存成功', icon: 'success' });
    } catch (error) {
      wx.hideLoading();
      wx.showToast({ title: '保存失败', icon: 'error' });
      console.error("保存首页顺序失败", error);
    } finally {
      this.setData({ isOrderingMode: false });
      this.fetchAndGroupPhotos(); // 重新加载以确认最终状态
    }
  },

  cancelOrdering() {
    this.setData({ isOrderingMode: false });
    // 恢复照片原始的 order 状态
    this.updatePhotoOrderNumbers(this.data.originalHomepageOrder);
  },

  // 5. 上传逻辑
  async onUploadTap() {
    try {
      const res = await wx.chooseMedia({
        count: 9,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
      });

      wx.showLoading({ title: '上传中...' });

      const uploadTasks = res.tempFiles.map(file => this.uploadFile(file.tempFilePath));
      await Promise.all(uploadTasks);

      wx.hideLoading();
      wx.showToast({ title: '上传成功', icon: 'success' });
      this.fetchAndGroupPhotos(); // 上传后刷新

    } catch (err) {
      // a `cancel` error will be thrown if user cancels
      if (err.errMsg.includes('cancel')) {
        return;
      }
      wx.hideLoading();
      wx.showToast({ title: '选择失败', icon: 'error' });
      console.error("选择或上传文件失败", err);
    }
  },

  uploadFile(filePath) {
    const cloudPath = `album/${Date.now()}-${Math.floor(Math.random() * 10000)}.jpg`;
    return wx.cloud.uploadFile({
      cloudPath,
      filePath,
    }).then(res => {
      return wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'addAlbumPhoto',
          photoInfo: {
            fileID: res.fileID,
            createTime: new Date(),
          }
        }
      });
    });
  },
})
