# 讲师预约限制修复说明

## 问题描述

之前系统允许讲师预约自己的课程，这在业务逻辑上是不合理的。讲师应该是课程的提供者，而不是参与者。

## 修复内容

### 1. 前端修改 (`miniprogram/utils/bookingUtils.js`)

添加了新的检查函数 `checkIfUserIsCoach`：

```javascript
/**
 * 检查用户是否为课程讲师
 * @param {Object} course 课程信息
 * @param {Object} userInfo 用户信息
 * @param {Object} page 页面实例
 * @returns {boolean} 如果是讲师返回true（不能预约），否则返回false（可以预约）
 */
export const checkIfUserIsCoach = (course, userInfo, page) => {
  // 检查用户是否有讲师角色
  const userRoles = userInfo.roles || [];
  const isCoach = userRoles.includes('讲师');
  
  // 如果用户不是讲师，可以预约
  if (!isCoach) {
    return false;
  }
  
  // 如果用户是讲师，检查是否为该课程的讲师
  const courseCoaches = course.coach || [];
  const userOpenid = userInfo.openid;
  
  // 检查用户openid是否在课程的讲师列表中
  if (courseCoaches.includes(userOpenid)) {
    showToast(page, { message: '讲师不能预约自己的课程', theme: 'warning' });
    return true; // 是该课程的讲师，不能预约
  }
  
  return false; // 不是该课程的讲师，可以预约
};
```

并在 `bookCourse` 函数中添加了这个检查：

```javascript
export const bookCourse = async (course, page, onSuccess, onError) => {
  // 检查登录状态
  const userInfo = checkUserLogin(page);
  if (!userInfo) return;

  // 检查课程可用性
  if (!checkCourseAvailability(course, page)) return;

  // 检查用户是否为该课程的讲师 (新增)
  if (checkIfUserIsCoach(course, userInfo, page)) return;

  // ... 其他检查逻辑
};
```

### 2. 后端修改 (`cloudfunctions/bookingManagement/index.js`)

在云函数的 `bookCourse` 函数中添加了服务端验证：

```javascript
// 4. 检查用户是否为该课程的讲师
const userRes = await db.collection('users').where({ openid: userId }).get();
if (userRes.data.length > 0) {
  const user = userRes.data[0];
  const userRoles = user.roles || [];
  const isCoach = userRoles.includes('讲师');

  // 如果用户是讲师，检查是否为该课程的讲师
  if (isCoach) {
    const courseCoaches = course.coach || [];
    if (courseCoaches.includes(userId)) {
      return { success: false, message: '讲师不能预约自己的课程' };
    }
  }
}
```

### 3. UI优化 (`miniprogram/pages/course-detail/course-detail.js`)

优化了课程详情页面的用户界面逻辑：

```javascript
// 检查是否为该课程的讲师
const courseCoaches = course.coach || [];
isCourseCoach = isCoach && courseCoaches.includes(userInfo.openid);

// 在setData中使用精确的角色判断
isCoach: isCourseCoach, // 只有该课程的讲师才显示讲师界面
```

**改进说明**：
- 之前：任何有讲师角色的用户都会看到讲师界面
- 现在：只有该课程的讲师才会看到讲师界面，其他讲师仍然看到学员界面
- 好处：讲师可以预约其他讲师的课程，只是不能预约自己的课程

## 影响范围

这个修复会影响以下页面的预约功能：

1. **课程表页面** (`pages/schedule/schedule.js`) - 使用 `bookCourseUtil`
2. **课程详情页面** (`pages/course-detail/course-detail.js`) - 使用 `bookCourse`
3. **其他使用统一预约逻辑的页面**

## 测试方法

### 测试场景1：普通学员预约课程
1. 使用学员账号登录
2. 尝试预约任意课程
3. **预期结果**：预约成功（如果满足其他条件）

### 测试场景2：讲师预约其他讲师的课程
1. 使用讲师账号登录
2. 尝试预约其他讲师的课程
3. **预期结果**：预约成功（如果满足其他条件）

### 测试场景3：讲师预约自己的课程
1. 使用讲师账号登录
2. 尝试预约自己创建的课程
3. **预期结果**：显示提示"讲师不能预约自己的课程"，预约失败

### 测试场景4：管理员预约课程
1. 使用管理员账号登录
2. 尝试预约任意课程
3. **预期结果**：预约成功（如果满足其他条件）

### 测试场景5：UI界面测试（课程详情页）
1. 使用讲师账号登录
2. 查看自己的课程详情页
3. **预期结果**：显示"查看预约学员名单"按钮，不显示预约按钮
4. 查看其他讲师的课程详情页
5. **预期结果**：显示预约相关按钮（如果有学员角色）

### 测试场景6：多角色用户测试
1. 使用既是讲师又是学员的账号登录
2. 查看自己的课程
3. **预期结果**：显示讲师界面
4. 查看其他讲师的课程
5. **预期结果**：显示学员界面，可以正常预约

## 技术细节

### 判断逻辑
1. 检查用户是否有"讲师"角色
2. 如果是讲师，检查用户的 openid 是否在课程的 coach 数组中
3. 如果在，则阻止预约并显示提示

### 数据结构
- 用户表 (`users`): `{ openid, roles: ['学员', '讲师', '管理员'] }`
- 课程表 (`courses`): `{ coach: ['讲师openid1', '讲师openid2'] }`

### 安全性
- 前端检查：提供即时的用户反馈
- 后端检查：确保数据安全，防止绕过前端验证

## 注意事项

1. 这个修复不影响讲师查看自己课程的预约学员名单
2. 讲师仍然可以管理自己的课程（编辑、删除等）
3. 管理员不受此限制影响
4. 如果一个用户既是讲师又是学员，只有在预约自己的课程时才会被阻止
