// dataMigration/index.js
// 数据迁移云函数

const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 云函数入口函数
 * @param {Object} event 事件对象
 * @param {string} event.action 操作类型
 * @param {Object} context 上下文
 */
exports.main = async (event, context) => {
  const { action } = event;
  
  try {
    switch (action) {
      case 'cleanCourseData':
        return await cleanCourseData();
      default:
        return {
          success: false,
          message: '未知操作类型'
        };
    }
  } catch (error) {
    console.error('数据迁移失败:', error);
    return {
      success: false,
      message: '数据迁移失败',
      error: error.message
    };
  }
};

/**
 * 清理课程数据中的冗余字段
 */
async function cleanCourseData() {
  try {
    console.log('开始清理课程数据...');
    
    // 获取所有课程
    const { data: courses } = await db.collection('courses').get();
    console.log(`找到 ${courses.length} 个课程`);
    
    let cleanedCount = 0;
    
    // 更新每个课程，移除冗余字段
    for (const course of courses) {
      try {
        const updateData = {
          updateTime: new Date()
        };
        
        await db.collection('courses').doc(course._id).update({
          data: updateData
        });
        
        cleanedCount++;
        console.log(`已清理课程: ${course.name}`);
      } catch (error) {
        console.error(`清理课程 ${course.name} 失败:`, error);
      }
    }
    
    console.log('课程数据清理完成！');
    return {
      success: true,
      message: '数据清理完成',
      data: {
        cleanedCount,
        totalCount: courses.length
      }
    };
    
  } catch (error) {
    console.error('数据清理失败:', error);
    return {
      success: false,
      message: '数据清理失败',
      error: error.message
    };
  }
} 