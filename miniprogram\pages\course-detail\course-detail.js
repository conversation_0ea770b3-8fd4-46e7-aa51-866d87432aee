// course-detail.js
// 课程详情页面逻辑文件
// 这是小程序的课程详情页面，负责展示课程详细信息、预约管理、学员列表等功能
// 类似于Web应用的商品详情页面或移动应用的活动详情页面

/**
 * 模块导入说明
 *
 * 这个页面涉及复杂的业务逻辑：
 * 1. 数据库操作：课程详情查询、用户预约查询
 * 2. 状态管理：预约状态、用户权限、课程状态
 * 3. 业务逻辑：预约、取消预约、权限控制
 * 4. UI交互：加载状态、错误处理、消息提示
 *
 * 导入方式对比：
 * - 具名导入：import { function1, function2 } from 'module'
 * - 命名空间导入：import * as ModuleName from 'module'
 * - 默认导入：import ModuleName from 'module'
 */

// 数据库操作相关函数
import { getCourseDetail, getUserBookings } from '../../utils/database.js';

// 系统常量定义
import { BOOKING_STATUS } from '../../utils/constants.js';

// Toast工具函数（命名空间导入）
// 使用 * as ToastUtils 的方式导入，调用时需要 ToastUtils.showToast()
// 这种方式可以避免命名冲突，特别是当有多个模块都有相同函数名时
import * as ToastUtils from '../../utils/toast.js';

// 预约相关业务逻辑函数
import { bookCourse, cancelBooking, loadSystemSettings } from '../../utils/bookingUtils.js';

/**
 * Page()函数：注册课程详情页面
 *
 * 页面功能：
 * 1. 课程信息展示：名称、时间、地点、讲师、描述等
 * 2. 预约管理：预约、取消预约、预约状态显示
 * 3. 权限控制：根据用户角色显示不同功能
 * 4. 学员管理：讲师和管理员可查看预约学员列表
 * 5. 状态判断：课程状态（可预约、已满、已结束等）
 *
 * 页面特点：
 * - 状态驱动：根据不同状态显示不同UI和功能
 * - 权限分级：学员、讲师、管理员有不同的操作权限
 * - 实时更新：预约状态实时反映到UI上
 */
Page({
  /**
   * data: 页面数据对象
   *
   * 数据分类：
   * 1. 课程数据：courseDetail（课程详细信息）
   * 2. 用户状态：isBooked, userInfo, userRoles（用户相关状态）
   * 3. 权限标识：isStudent, isCoach, isAdmin（权限判断）
   * 4. UI状态：loading, loadingError, showStudentList（界面状态）
   * 5. 业务状态：courseStatus, isPrivateCourse（业务逻辑状态）
   * 6. 系统配置：cancelTimeLimit（系统设置）
   */
  data: {
    /**
     * 核心课程数据
     */

    // 课程详细信息对象：存储从数据库获取的完整课程信息
    // 数据类型：Object | null
    // 数据结构：{ id, name, startTime, endTime, venue, coach, description, capacity, bookings, ... }
    // 数据来源：getCourseDetail()方法从courses集合查询
    // null状态：页面初始化时为null，加载完成后为课程对象，加载失败时保持null
    // 用途：渲染课程详情页面的所有信息
    courseDetail: null,

    /**
     * 用户预约状态
     */

    // 当前用户是否已预约此课程：控制预约按钮的状态和文字
    // 数据类型：boolean
    // true：用户已预约，显示"取消预约"按钮
    // false：用户未预约，显示"立即预约"按钮
    // 计算逻辑：在loadCourseDetail()中通过查询用户预约记录确定
    // 更新时机：页面加载时、预约/取消预约操作后
    isBooked: false,

    /**
     * 页面状态管理
     */

    // 加载错误状态：标识数据加载是否失败
    // 数据类型：boolean
    // true：数据加载失败，显示错误提示和重试按钮
    // false：数据加载成功或正在加载中
    // 设置时机：getCourseDetail()方法捕获异常时设为true
    // 用户操作：点击重试按钮时重新加载数据
    loadingError: false,

    // 是否显示图片占位符：控制图片加载失败时的兜底显示
    // 数据类型：boolean
    // true：显示灰色占位符（图片加载失败时）
    // false：显示实际图片（图片加载成功时）
    // 初始值：false，尝试加载图片
    showImagePlaceholder: false,

    /**
     * 课程图片相关状态
     */

    // 课程图片列表：存储课程的所有图片信息
    // 数据类型：Array<Object>
    // 数据结构：[{ fileID, tempFileURL, index }, ...]
    // 数据来源：从课程数据的images字段获取fileID，然后获取临时访问URL
    // 用途：在课程详情页展示课程图片
    courseImages: [],

    // 图片预览状态：控制图片预览弹窗的显示
    // 数据类型：boolean
    // true：显示图片预览弹窗
    // false：隐藏图片预览弹窗
    imagePreviewVisible: false,

    // 当前预览的图片索引：用于图片预览时的定位
    // 数据类型：number
    // 用途：在图片预览弹窗中定位当前显示的图片
    currentImageIndex: 0,

    // 轮播图高度：用于动态设置轮播图容器高度
    // 数据类型：number
    // 用途：根据图片实际高度动态调整轮播图容器高度
    swiperHeight: 200,

    /**
     * 用户信息和权限
     */

    // 当前用户信息：存储登录用户的基础信息
    // 数据类型：Object | null
    // 数据结构：{ openid, nickName, avatarUrl, roles, ... }
    // 数据来源：wx.getStorageSync('userInfo')从本地存储获取
    // null状态：用户未登录或登录信息已过期
    // 用途：权限判断、用户身份验证、个性化显示
    userInfo: null,

    // 用户角色数组：存储用户拥有的所有角色
    // 数据类型：Array<string>
    // 可能值：['学员'], ['讲师'], ['管理员'], ['学员', '讲师'] 等
    // 数据来源：userInfo.roles字段
    // 用途：权限控制、功能显示控制
    // 多角色：一个用户可以同时拥有多个角色
    userRoles: [],

    /**
     * 权限标识（计算属性）
     *
     * 这些布尔值根据userRoles数组计算得出
     * 目的：简化WXML模板中的条件判断
     * 更新时机：用户信息加载完成后计算
     */

    // 是否为学员：用户是否具有学员角色
    // 计算逻辑：userRoles.includes('学员')
    // 权限范围：可以预约课程、查看自己的预约记录
    isStudent: false,

    // 是否为该课程的讲师：用户是否为当前课程的讲师
    // 计算逻辑：userRoles.includes('讲师') && course.coach.includes(userInfo.openid)
    // 权限范围：可以查看该课程的学员列表、管理该课程
    // 注意：只有该课程的讲师才会显示讲师界面，其他讲师仍显示学员界面
    isCoach: false,

    // 是否为管理员：用户是否具有管理员角色
    // 计算逻辑：userRoles.includes('管理员')
    // 权限范围：可以查看所有课程的学员列表、管理所有课程
    isAdmin: false,

    /**
     * UI交互状态
     */

    // 是否显示学员列表：控制学员列表弹窗的显示状态
    // 数据类型：boolean
    // true：显示学员列表弹窗，通常在讲师或管理员点击"查看学员"时
    // false：隐藏学员列表弹窗
    // 权限控制：只有讲师和管理员可以查看学员列表
    showStudentList: false,

    /**
     * 学员管理相关
     */

    // 预约学员列表：存储已预约此课程的学员信息
    // 数据类型：Array<StudentObject>
    // 数据结构：[{ openid, nickName, avatarUrl, bookingTime, ... }]
    // 数据来源：courseDetail.bookings字段，经过用户信息关联查询
    // 权限控制：只有讲师和管理员可以查看
    // 用途：显示在学员列表弹窗中，便于讲师了解参课学员
    studentList: [],

    /**
     * 课程状态管理
     */

    // 课程状态文字描述：用于在UI上显示课程的当前状态
    // 数据类型：string
    // 可能的值：
    //   - '可预约'：课程未满，用户可以预约
    //   - '已满'：课程已达到容量上限
    //   - '已结束'：课程时间已过
    //   - '已预约'：当前用户已预约此课程
    //   - '维护中'：系统维护状态
    // 计算逻辑：在updateCourseStatus()方法中根据多个条件综合判断
    // 显示位置：课程详情页面的状态标签
    courseStatus: '可预约',

    // 页面加载状态：控制loading动画的显示
    // 数据类型：boolean
    // true：正在加载数据，显示loading动画，禁用用户操作
    // false：加载完成，隐藏loading动画，允许用户操作
    // 设置时机：开始加载时设为true，加载完成（成功或失败）后设为false
    // 用户体验：防止用户在数据未加载完成时进行操作
    loading: false,

    /**
     * 系统设置相关
     *
     * 这些设置从云数据库的系统配置表中获取
     * 不使用硬编码的默认值，确保配置的灵活性
     * 管理员可以在系统设置页面修改这些参数
     */

    // 取消预约时间限制（分钟）：控制预约取消的时间规则
    // 数据类型：number | null
    // 单位：分钟（例如：180表示课程开始前3小时内不能取消）
    // null状态：尚未从数据库加载，避免使用可能过时的默认值
    // 数据来源：loadSystemSettings()方法从systemSettings集合获取
    // 业务逻辑：在cancelBooking()方法中用于判断是否允许取消预约
    // 用户提示：取消按钮会根据此设置显示相应的提示信息
    cancelTimeLimit: null,
  },

  /**
   * onLoad: 页面生命周期函数 - 页面加载时调用
   *
   * 执行顺序：onLoad → onShow → onReady
   *
   * 主要任务：
   * 1. 初始化页面状态（隐藏TabBar）
   * 2. 加载系统设置（预约规则等）
   * 3. 根据传入参数加载课程详情
   * 4. 处理参数异常情况
   *
   * @param {Object} options - 页面参数对象
   *   - options.id: 课程ID，从上一页面传递过来
   */
  onLoad(options) {
    /**
     * 第一步：初始化页面UI状态
     */

    // 隐藏TabBar导航栏：课程详情页不需要底部导航
    // 防御性编程：先检查方法是否存在，再检查TabBar实例是否存在
    // typeof检查：确保getTabBar方法存在，避免调用undefined方法
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      // setSelectedValue(-1)：设置TabBar无选中状态
      // -1表示没有任何Tab被选中，适用于详情页面
      this.getTabBar().setSelectedValue(-1);
    }

    /**
     * 第二步：加载系统配置
     */

    // 加载系统设置：获取影响业务逻辑的系统配置参数
    // 配置内容：取消预约时间限制、维护模式状态等
    // 加载时机：页面初始化时立即加载，确保后续业务逻辑有正确的配置
    // 异步执行：不阻塞页面加载，配置加载完成后自动更新相关状态
    this.loadSystemSettings();

    /**
     * 第三步：加载课程详情数据
     */

    // 根据传入的课程ID加载课程详情：核心业务逻辑
    // options.id：从URL参数或页面跳转参数中获取的课程ID
    if (options.id) {
      // 调用课程详情加载方法
      // 参数解释：
      // - options.id: 课程ID，用于数据库查询
      // - 0: 重试次数，首次加载为0，失败重试时递增
      // - true: 是否显示loading动画，首次加载需要显示给用户
      this.loadCourseDetail(options.id, 0, true);
    } else {
      /**
       * 参数异常处理：缺少必要的课程ID参数
       */

      // 设置错误状态：页面会显示错误提示和操作按钮
      this.setData({
        loadingError: true,     // 设置加载错误状态，触发错误UI显示
        loading: false          // 关闭加载状态，停止loading动画
      });

      // 错误处理说明：
      // 1. 用户看到错误提示信息
      // 2. 可以点击返回按钮回到上一页
      // 3. 可以点击重试按钮（但由于没有ID，重试也会失败）
    }
  },

  /**
   * onShow: 页面生命周期函数 - 页面显示时调用
   *
   * 触发时机：
   * 1. 页面首次显示（在onLoad之后）
   * 2. 从其他页面返回到当前页面
   * 3. 从后台切换到前台
   * 4. 从下级页面返回
   *
   * 设计考虑：
   * - 每次显示都刷新数据，确保信息是最新的
   * - 重新隐藏TabBar，防止从其他页面返回时状态异常
   * - 使用可选链操作符(?.)安全地访问数据
   */
  onShow() {
    // 每次显示页面时都隐藏TabBar
    // 这是防御性编程，确保TabBar状态正确
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setSelectedValue(-1);
    }

    // 重新加载系统设置
    // 系统设置可能在其他页面被修改，需要重新加载
    this.loadSystemSettings();

    // 重新加载课程详情
    // 使用可选链操作符(?.)安全地访问courseDetail.id
    // 如果courseDetail为null或undefined，不会执行后续代码
    if (this.data.courseDetail?.id) {
      // 参数说明：
      // - this.data.courseDetail.id: 当前课程ID
      // - 0: 重试次数
      // - false: 不显示loading（避免闪烁）
      this.loadCourseDetail(this.data.courseDetail.id, 0, false);
    }
  },

  /**
   * onPullDownRefresh: 下拉刷新事件处理
   *
   * 下拉刷新机制：
   * 1. 用户在页面顶部下拉
   * 2. 触发onPullDownRefresh事件
   * 3. 执行数据刷新逻辑
   * 4. 调用wx.stopPullDownRefresh()结束刷新状态
   *
   * 需要在页面配置文件中启用：
   * {
   *   "enablePullDownRefresh": true,
   *   "backgroundTextStyle": "dark"
   * }
   *
   * 异步处理：
   * 使用async/await处理异步操作，确保刷新完成后再结束loading状态
   */
  async onPullDownRefresh() {
    /**
     * 数据有效性检查
     */

    // 检查是否有课程数据可以刷新：确保有有效的课程ID
    // 可选链操作符(?.)：安全地访问嵌套属性，避免null/undefined错误
    // this.data.courseDetail?.id 等价于：
    // this.data.courseDetail && this.data.courseDetail.id
    if (this.data.courseDetail?.id) {
      try {
        /**
         * 并行刷新数据
         *
         * 使用await确保异步操作按顺序完成
         * 先刷新系统设置，再刷新课程详情
         */

        // 刷新系统设置：获取最新的系统配置（如取消时间限制）
        // await关键字：等待异步操作完成再继续执行
        await this.loadSystemSettings();

        // 刷新课程详情：获取最新的课程信息和预约状态
        // 参数说明：
        // - this.data.courseDetail.id: 当前课程的ID
        // - 0: 重试次数，下拉刷新时重置为0
        // - false: 不显示loading动画，因为下拉刷新本身有动画
        await this.loadCourseDetail(this.data.courseDetail.id, 0, false);

        /**
         * 成功反馈
         */

        // 显示刷新成功提示：给用户明确的操作反馈
        // ToastUtils.showToast：使用工具函数显示Toast消息
        ToastUtils.showToast(this, {
          message: '刷新成功',    // 提示文字
          theme: 'success'       // 成功主题，通常显示为绿色
        });
      } catch (error) {
        /**
         * 错误处理
         */

        // 记录错误日志：便于调试和问题排查
        console.error('下拉刷新失败:', error);

        // 显示刷新失败提示：告知用户操作失败
        ToastUtils.showToast(this, {
          message: '刷新失败',    // 提示文字
          theme: 'error'         // 错误主题，通常显示为红色
        });
      }
    }

    /**
     * 结束刷新状态
     */

    // 停止下拉刷新的loading动画：恢复页面正常状态
    // 重要：无论成功还是失败，都必须调用这个方法
    // 否则页面会一直显示刷新状态，影响用户体验
    wx.stopPullDownRefresh();
  },

  /**
   * loadSystemSettings: 加载系统设置的异步方法
   *
   * 功能说明：
   * 从云数据库获取系统配置参数，影响页面的业务逻辑
   * 主要获取取消预约时间限制等关键配置
   *
   * 调用时机：
   * 1. 页面初始化时（onLoad）
   * 2. 下拉刷新时（onPullDownRefresh）
   * 3. 从其他页面返回时（onShow）
   */
  async loadSystemSettings() {
    try {
      // 调试日志：记录系统设置加载开始
      console.log('开始加载系统设置...');

      /**
       * 调用工具函数加载系统设置
       *
       * loadSystemSettings函数：
       * - 参数：this（当前页面实例）
       * - 返回：取消时间限制（分钟）
       * - 副作用：自动更新this.data.cancelTimeLimit
       */
      const cancelTimeLimit = await loadSystemSettings(this);

      // 调试日志：记录加载结果
      console.log('取消时间限制已设置:', cancelTimeLimit);

      /**
       * 更新课程详情中的取消政策显示
       *
       * 条件：课程详情已经加载完成
       * 目的：在系统设置更新后，同步更新课程详情中的相关显示
       */
      if (this.data.courseDetail) {
        // 创建课程详情的副本：避免直接修改原对象
        // 扩展运算符(...)：创建对象的浅拷贝
        const courseDetail = { ...this.data.courseDetail };

        // 更新取消政策文字：根据最新的系统设置格式化显示文字
        // formatCancelPolicy()：格式化取消政策的工具方法
        courseDetail.cancelPolicy = this.formatCancelPolicy();

        // 更新页面数据：触发页面重新渲染
        this.setData({ courseDetail });
      }
    } catch (error) {
      /**
       * 错误处理
       *
       * 处理策略：
       * 1. 记录错误日志，便于调试
       * 2. 不显示用户错误提示，避免影响主要功能
       * 3. 使用默认配置继续运行
       */
      console.error('加载系统设置失败:', error);

      // 注意：这里不显示用户错误提示
      // 因为系统设置加载失败不应该阻止用户查看课程详情
      // 页面会使用默认的配置值继续运行
    }
  },

  // 加载课程详情
  async loadCourseDetail(courseId, retryCount = 0, showLoading = true) {
    try {
      if (showLoading) {
        ToastUtils.showLoading(this, '加载中...');
      }
      
      // 先尝试直接查询数据库
      const db = wx.cloud.database();
      
      let course;
      try {
        const result = await db.collection('courses').doc(courseId).get();
        course = result.data;
      } catch (directError) {
        course = await getCourseDetail(courseId);
      }
      
      if (!course) {
        this.setData({ loading: false });
        ToastUtils.showToast(this, { message: '课程不存在或已下架', theme: 'error' });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }
      
      // 获取用户信息
      const app = getApp();
      let userInfo = null;
      let userRoles = [];
      let isStudent = false;
      let isCoach = false;
      let isAdmin = false;
      let isCourseCoach = false; // 是否为该课程的讲师

      try {
        if (app.isLoggedIn()) {
          userInfo = app.getUserInfo();
          userRoles = userInfo.roles || [];
          isStudent = userRoles.includes('学员');
          isCoach = userRoles.includes('讲师');
          isAdmin = userRoles.includes('管理员');

          // 检查是否为该课程的讲师
          const courseCoaches = course.coach || [];
          isCourseCoach = isCoach && courseCoaches.includes(userInfo.openid);
        }
      } catch (userError) {
        // 用户信息获取失败，继续执行
      }
      
      // 检查用户是否已预约该课程
      let isBooked = false;
      let bookedCount = 0;
      let remaining = course.capacity || 0;
      let available = remaining > 0;
      
      try {
        if (app.isLoggedIn()) {
          const userBookings = await getUserBookings(userInfo.openid);
          isBooked = userBookings.some(b => 
            String(b.courseId) === String(course._id) && 
            b.status === BOOKING_STATUS.UPCOMING
          );
        }
        
        // 计算剩余名额和可用状态
        const bookingCount = await db.collection('bookings')
          .where({
            courseId: course._id,
            status: BOOKING_STATUS.UPCOMING
          })
          .count();
        
        bookedCount = bookingCount.total;
        remaining = course.capacity - bookedCount;
        available = remaining > 0;
      } catch (bookingError) {
        // 预约信息获取失败，使用默认值
      }
      
      // 判断课程状态
      const now = new Date();
      const courseStartTime = new Date(course.startTime);
      let courseStatus = '可预约';
      
      if (courseStartTime < now) {
        courseStatus = '已结束';
      } else if (isBooked) {
        courseStatus = '已预约';
      } else if (remaining <= 0) {
        courseStatus = '已满';
      }

      // 获取讲师详细信息
      const coaches = [];
      try {
        if (course.coach && course.coach.length > 0) {
          const { data: coachInfos } = await db.collection('coachInfo')
            .where({
              openid: db.command.in(course.coach)
            })
            .get();
          
          const { data: users } = await db.collection('users')
            .where({
              openid: db.command.in(course.coach)
            })
            .get();
          
          // 合并讲师信息
          course.coach.forEach((coachOpenid, index) => {
            const coachInfo = coachInfos.find(c => c.openid === coachOpenid) || {};
            const user = users.find(u => u.openid === coachOpenid) || {};
            coaches.push({
              id: index + 1,
              name: user.nickName || '未知讲师',
              avatar: user.avatarUrl,
              specialty: coachInfo.specialties || '健身讲师',
              introduction: coachInfo.introduction || ''
            });
          });
        }
      } catch (coachError) {
        // 讲师信息获取失败，继续执行
      }
      
      // 兼容 activityDetail 为对象或字符串
      let activityDetailField = '';
      if (typeof course.activityDetail === 'string') {
        activityDetailField = course.activityDetail;
      } else if (course.activityDetail && typeof course.activityDetail === 'object') {
        activityDetailField = course.activityDetail.description || '';
      }

      // 设置课程详情数据
      const courseDetail = {
        id: course._id,
        name: course.name,
        type: course.type,
        date: this.formatDate(new Date(course.startTime)),
        time: `${this.formatTime(new Date(course.startTime))}-${this.formatTime(new Date(course.endTime))}`,
        duration: this.calculateDuration(`${this.formatTime(new Date(course.startTime))}-${this.formatTime(new Date(course.endTime))}`),
        venue: course.venue,
        coaches: coaches,
        remaining: remaining,
        capacity: course.capacity,
        bookedCount: bookedCount,
        cancelPolicy: this.formatCancelPolicy(),
        available: available,
        status: course.status || 'offline', // 课程上线状态
        startTime: course.startTime, // 添加开始时间用于取消时间检查
        activityDetail: { description: activityDetailField } // 保证有 description 字段
      };
      
      this.setData({
        courseDetail,
        isBooked,
        loadingError: false,
        userInfo,
        userRoles,
        isStudent,
        isCoach: isCourseCoach, // 只有该课程的讲师才显示讲师界面
        isAdmin,
        courseStatus,
        loading: false
      });

      // 加载课程图片
      if (course.images && course.images.length > 0) {
        this.loadCourseImages(course.images);
      } else {
        this.setData({
          courseImages: [],
          swiperHeight: 200 // 重置轮播图高度
        });
      }

      if (showLoading) {
        ToastUtils.hideToast(this);
      }
    } catch (error) {
      console.error('加载课程详情失败:', error);
      if (showLoading) {
        ToastUtils.hideToast(this);
      }
      
      if (retryCount < 2) {
        // 自动重试最多2次
        ToastUtils.showToast(this, { 
          message: `加载失败，正在重试(${retryCount + 1}/2)`,
          theme: 'warning',
          duration: 1500
        });
        setTimeout(() => {
          this.loadCourseDetail(courseId, retryCount + 1, true);
        }, 1500);
      } else {
        this.setData({
          loadingError: true,
          loading: false
        });
        wx.showModal({
          title: '加载失败',
          content: '网络不稳定或服务器异常，请检查网络后重试',
          showCancel: false,
          confirmText: '重试',
          success: (res) => {
            if (res.confirm) {
              this.loadCourseDetail(courseId, 0, true);
            }
          }
        });
      }
    }
  },

  // 格式化日期
  formatDate(date) {
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const weekday = weekdays[date.getDay()];
    return `${year}年${month}月${day}日（${weekday}）`;
  },

  // 格式化时间
  formatTime(date) {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  },

  // 计算课程时长
  calculateDuration(timeStr) {
    const times = timeStr.split('-');
    if (times.length === 2) {
      const start = new Date(`2000-01-01 ${times[0]}`);
      const end = new Date(`2000-01-01 ${times[1]}`);
      const diff = end - start;
      const minutes = Math.floor(diff / (1000 * 60));
      return `${minutes}分钟`;
    }
    return '60分钟';
  },

  // 格式化取消政策（从数据库获取时间限制）
  formatCancelPolicy() {
    const cancelTimeLimit = this.data.cancelTimeLimit;
    if (cancelTimeLimit === null || cancelTimeLimit === undefined) {
      return '系统配置中';
    }
    
    const hours = Math.floor(cancelTimeLimit / 60);
    const minutes = cancelTimeLimit % 60;
    
    if (hours > 0 && minutes > 0) {
      return `课程开始前${hours}小时${minutes}分钟`;
    } else if (hours > 0) {
      return `课程开始前${hours}小时`;
    } else {
      return `课程开始前${minutes}分钟`;
    }
  },

  // 手动重试
  onRetry() {
    if (this.data.courseDetail?.id) {
      this.loadCourseDetail(this.data.courseDetail.id, 0, true);
    } else {
      wx.navigateBack();
    }
  },

  // 复制课程ID
  copyCourseId() {
    wx.setClipboardData({
      data: this.data.courseDetail.id,
      showToast: false, // 禁用系统默认提示
      success: () => {
        // 延迟显示自定义提示，确保系统提示完全被禁用
        setTimeout(() => {
          ToastUtils.showToast(this, { message: '课程ID已复制', theme: 'success' });
        }, 100);
      },
      fail: () => {
        ToastUtils.showToast(this, { message: '复制失败', theme: 'error' });
      }
    });
  },

  // 查看预约学员名单
  async showStudentList() {
    try {
      wx.showLoading({ title: '加载中...' });
      
      const db = wx.cloud.database();
      const { data: bookings } = await db.collection('bookings')
        .where({
          courseId: this.data.courseDetail.id,
          status: BOOKING_STATUS.UPCOMING
        })
        .get();
      
      // 获取学员信息
      const userIds = bookings.map(b => b.userId);
      const { data: users } = await db.collection('users')
        .where({
          openid: db.command.in(userIds)
        })
        .get();
      
      const studentList = bookings.map(booking => {
        const user = users.find(u => u.openid === booking.userId);
        return {
          id: booking._id,
          name: user ? user.nickName : '未知用户',
          avatar: user ? user.avatarUrl : '',
          bookTime: this.formatDate(new Date(booking.createTime))
        };
      });
      
      this.setData({
        studentList,
        showStudentList: true
      });
      
      ToastUtils.hideToast(this);
    } catch (error) {
      ToastUtils.hideToast(this);
      console.error('加载学员名单失败:', error);
      ToastUtils.showToast(this, { message: '加载失败', theme: 'error' });
    }
  },

  // 关闭学员名单
  closeStudentList() {
    this.setData({
      showStudentList: false
    });
  },

  // 编辑课程
  editCourse() {
    wx.navigateTo({
      url: `/pages/course-edit/course-edit?id=${this.data.courseDetail.id}`
    });
  },

  // 跳转到登录页面
  goToLogin() {
    wx.switchTab({
      url: '/pages/profile/profile'
    });
  },

  // 删除课程
  deleteCourse() {
    wx.showModal({
      title: '确认删除',
      content: `确定要删除课程"${this.data.courseDetail.name}"吗？此操作不可恢复。`,
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({ title: '删除中...' });
          
          try {
            const result = await wx.cloud.callFunction({
              name: 'adminManagement',
              data: {
                action: 'deleteCourse',
                courseId: this.data.courseDetail.id
              }
            });
            
            ToastUtils.hideToast(this);
            
            if (result.result.success) {
              ToastUtils.showToast(this, { message: '删除成功', theme: 'success' });
              
              setTimeout(() => {
                wx.navigateBack();
              }, 1500);
            } else {
              ToastUtils.showToast(this, { message: result.result.message || '删除失败', theme: 'error' });
            }
          } catch (error) {
            ToastUtils.hideToast(this);
            console.error('删除课程失败:', error);
            ToastUtils.showToast(this, { message: '删除失败，请重试', theme: 'error' });
          }
        }
      }
    });
  },

  /**
   * bookCourse: 预约课程的异步方法
   *
   * 功能说明：
   * 调用统一的预约工具函数，处理课程预约业务逻辑
   * 包括权限验证、容量检查、数据库操作、状态更新等
   *
   * 业务流程：
   * 1. 验证用户权限和课程状态
   * 2. 检查课程容量是否已满
   * 3. 创建预约记录到数据库
   * 4. 更新课程剩余名额
   * 5. 更新页面显示状态
   *
   * 错误处理：
   * 工具函数内部会处理各种错误情况并显示相应提示
   */
  async bookCourse() {
    // 调用统一的预约工具函数：确保预约逻辑的一致性
    // 参数说明：
    // 1. this.data.courseDetail: 当前课程的详细信息
    // 2. this: 当前页面实例，用于显示提示信息
    // 3. 成功回调函数
    // 4. 错误回调函数
    await bookCourse(this.data.courseDetail, this,
      /**
       * 成功回调函数：预约成功后的页面状态更新
       *
       * @param {Object} course - 预约成功后返回的课程信息（参数未使用但保持接口一致性）
       */
      (course) => {
        /**
         * 更新课程详情中的预约相关数据
         */

        // 获取当前课程详情的引用：直接修改现有对象
        const courseDetail = this.data.courseDetail;

        // 减少剩余名额：预约成功后可用名额减1
        // 数据同步：确保页面显示与数据库状态一致
        courseDetail.remaining = courseDetail.remaining - 1;

        // 更新可用状态：根据剩余名额判断是否还可以预约
        // available字段：用于控制预约按钮的可用状态
        courseDetail.available = courseDetail.remaining > 0;

        /**
         * 批量更新页面状态
         *
         * 使用setData一次性更新多个状态，提高性能
         */
        this.setData({
          courseDetail,           // 更新课程详情（包含新的剩余名额）
          isBooked: true,         // 标记用户已预约此课程
          courseStatus: '已预约'   // 更新课程状态显示文字
        });
      },
      /**
       * 错误回调函数：预约失败后的处理
       *
       * @param {Error} error - 错误对象，包含失败原因
       */
      (error) => {
        // 记录错误日志：便于调试和问题排查
        console.error('预约失败:', error);

        // 注意：用户错误提示由bookCourse工具函数内部处理
        // 这里只记录日志，不需要额外的用户提示
      }
    );
  },

  /**
   * cancelBooking: 取消预约的异步方法
   *
   * 功能说明：
   * 调用统一的取消预约工具函数，处理取消预约业务逻辑
   * 包括时间限制检查、权限验证、数据库操作、状态更新等
   *
   * 业务流程：
   * 1. 检查取消时间限制
   * 2. 验证用户权限
   * 3. 删除预约记录
   * 4. 更新课程剩余名额
   * 5. 更新页面显示状态
   *
   * 时间限制：
   * 根据系统设置的cancelTimeLimit判断是否允许取消
   */
  async cancelBooking() {
    // 调用统一的取消预约工具函数：确保取消逻辑的一致性
    // 参数说明：
    // 1. this.data.courseDetail: 当前课程的详细信息
    // 2. this: 当前页面实例，用于显示提示信息
    // 3. 成功回调函数
    // 4. 错误回调函数
    await cancelBooking(this.data.courseDetail, this,
      /**
       * 成功回调函数：取消预约成功后的页面状态更新
       *
       * @param {Object} course - 取消成功后返回的课程信息（参数未使用但保持接口一致性）
       */
      (course) => {
        /**
         * 更新课程详情中的预约相关数据
         */

        // 获取当前课程详情的引用：直接修改现有对象
        const courseDetail = this.data.courseDetail;

        // 增加剩余名额：取消预约后可用名额加1
        // 数据同步：确保页面显示与数据库状态一致
        courseDetail.remaining = courseDetail.remaining + 1;

        // 更新可用状态：取消后课程重新变为可预约状态
        courseDetail.available = courseDetail.remaining > 0;

        /**
         * 批量更新页面状态
         *
         * 使用setData一次性更新多个状态，提高性能
         */
        this.setData({
          courseDetail,           // 更新课程详情（包含新的剩余名额）
          isBooked: false,        // 标记用户未预约此课程
          courseStatus: '可预约'   // 更新课程状态显示文字
        });
      },
      /**
       * 错误回调函数：取消预约失败后的处理
       *
       * @param {Error} error - 错误对象，包含失败原因
       */
      (error) => {
        // 记录错误日志：便于调试和问题排查
        console.error('取消预约失败:', error);

        // 注意：用户错误提示由cancelBooking工具函数内部处理
        // 这里只记录日志，不需要额外的用户提示
      }
    );
  },

  /**
   * onAvatarError: 头像加载错误处理
   *
   * 功能说明：
   * 当讲师头像加载失败时触发，可以设置默认头像或错误处理逻辑
   *
   * @param {Object} e - 事件对象，包含错误详情
   */
  onAvatarError(e) {
    // 记录头像加载失败的详细信息：便于调试图片加载问题
    console.log('头像加载失败:', e.detail);

    // 扩展功能：可以在这里设置默认头像逻辑
    // 例如：将失败的头像URL替换为默认头像URL
    // 或者隐藏头像显示，只显示讲师姓名
  },

  /**
   * onAvatarLoad: 头像加载成功处理
   *
   * 功能说明：
   * 当讲师头像加载成功时触发，可以用于统计或优化逻辑
   *
   * @param {Object} e - 事件对象，包含加载成功的详情
   */
  onAvatarLoad(e) {
    // 记录头像加载成功的信息：便于监控图片加载性能
    console.log('头像加载成功:', e.detail);

    // 扩展功能：可以在这里添加图片加载性能统计
    // 或者触发头像加载完成后的UI优化逻辑
  },

  /**
   * onImageLoad: 课程图片加载成功事件处理方法
   *
   * 当课程图片成功加载时调用
   * 确保显示实际图片而不是占位符
   *
   * @param {Object} e - 事件对象，包含图片加载成功的详情
   */
  onImageLoad(e) {
    console.log('课程图片加载成功:', e.detail);
    this.setData({
      showImagePlaceholder: false
    });
  },

  /**
   * onImageError: 课程图片加载失败事件处理方法
   *
   * 当课程图片加载失败时调用
   * 显示灰色占位符作为兜底方案
   *
   * @param {Object} e - 事件对象，包含图片加载失败的详情
   */
  onImageError(e) {
    console.log('课程图片加载失败:', e.detail);
    this.setData({
      showImagePlaceholder: true
    });
  },

  /**
   * 课程图片管理相关方法
   */

  /**
   * loadCourseImages: 加载课程图片的临时访问URL
   *
   * @param {Array} imageFileIds - 图片的fileID数组
   */
  async loadCourseImages(imageFileIds) {
    if (!imageFileIds || imageFileIds.length === 0) {
      this.setData({
        courseImages: [],
        swiperHeight: 200 // 重置轮播图高度
      });
      return;
    }

    try {
      const result = await wx.cloud.getTempFileURL({
        fileList: imageFileIds
      });

      const courseImages = result.fileList.map((file, index) => ({
        fileID: file.fileID,
        tempFileURL: file.tempFileURL,
        index: index
      }));

      this.setData({
        courseImages,
        swiperHeight: 200 // 重置轮播图高度，等待图片加载后重新计算
      });
    } catch (error) {
      console.error('获取课程图片临时URL失败:', error);
      // 图片加载失败不影响页面主要功能，只记录错误
      this.setData({
        courseImages: [],
        swiperHeight: 200 // 重置轮播图高度
      });
    }
  },

  /**
   * onPreviewCourseImage: 预览课程图片事件处理
   *
   * @param {Object} e - 事件对象
   */
  onPreviewCourseImage(e) {
    const { index } = e.currentTarget.dataset;
    const urls = this.data.courseImages.map(img => img.tempFileURL);

    wx.previewImage({
      current: urls[index],
      urls: urls
    });
  },

  /**
   * onSwiperImageLoad: 轮播图图片加载完成事件处理
   *
   * @param {Object} e - 事件对象
   */
  onSwiperImageLoad(e) {
    const { index } = e.currentTarget.dataset;
    const { width, height } = e.detail;

    // 获取系统信息，计算容器宽度
    const systemInfo = wx.getSystemInfoSync();
    const windowWidth = systemInfo.windowWidth;
    const containerWidth = windowWidth - 32; // 减去左右边距

    // 计算图片应该显示的高度（保持宽高比）
    const imageHeight = (height * containerWidth) / width;

    // 只在第一张图片加载时设置高度，或者当前图片高度更大时更新
    if (index === 0 || imageHeight > this.data.swiperHeight) {
      this.setData({
        swiperHeight: imageHeight
      });
    }
  }
});

/**
 * 文件总结：course-detail.js
 *
 * 这个文件实现了一个功能完整的课程详情页面，是小程序中的核心页面之一。
 *
 * 主要特点：
 *
 * 1. 复杂的数据管理：
 *    - 多维度数据：课程信息、用户状态、权限标识、UI状态等
 *    - 状态驱动：根据不同状态显示不同的UI和功能
 *    - 实时更新：预约状态变化时实时更新页面显示
 *    - 数据关联：课程、用户、预约、讲师等多表数据关联
 *
 * 2. 权限控制系统：
 *    - 多角色支持：学员、讲师、管理员有不同的操作权限
 *    - 动态权限：根据用户角色动态显示功能按钮
 *    - 安全验证：在客户端和服务端都进行权限验证
 *
 * 3. 完整的业务流程：
 *    - 课程展示：详细的课程信息展示
 *    - 预约管理：预约、取消预约的完整流程
 *    - 学员管理：讲师和管理员可查看学员列表
 *    - 课程管理：管理员可编辑和删除课程
 *
 * 4. 用户体验优化：
 *    - 加载状态：完整的loading状态管理
 *    - 错误处理：网络错误、数据错误的友好处理
 *    - 重试机制：自动重试和手动重试功能
 *    - 下拉刷新：支持下拉刷新获取最新数据
 *
 * 5. 技术特点：
 *    - 异步处理：大量使用async/await处理异步操作
 *    - 错误边界：完善的try-catch错误处理
 *    - 数据验证：输入数据的有效性验证
 *    - 性能优化：合理的数据加载和状态更新策略
 *
 * 业务价值：
 *
 * 1. 信息展示：
 *    - 完整的课程信息展示，帮助用户了解课程详情
 *    - 讲师信息展示，增强用户对课程的信任度
 *    - 实时的预约状态，帮助用户做出预约决策
 *
 * 2. 预约管理：
 *    - 便捷的预约流程，提升用户体验
 *    - 智能的取消限制，平衡用户需求和运营需要
 *    - 实时的名额显示，避免超额预约
 *
 * 3. 运营支持：
 *    - 学员列表功能，帮助讲师了解参课学员
 *    - 课程管理功能，支持课程的动态调整
 *    - 数据统计功能，为运营决策提供数据支持
 *
 * 与您熟悉的技术对比：
 *
 * - 数据绑定：类似于WPF的数据绑定机制
 * - 状态管理：类似于React的状态管理
 * - 权限控制：类似于ASP.NET的角色权限系统
 * - 异步处理：类似于C#的async/await模式
 * - 错误处理：类似于.NET的异常处理机制
 *
 * 学习价值：
 *
 * 这个文件展示了如何在前端实现复杂的业务逻辑：
 * 1. 多维度的数据状态管理
 * 2. 基于角色的权限控制系统
 * 3. 完整的用户交互流程设计
 * 4. 健壮的错误处理和用户体验优化
 * 5. 前后端协作的最佳实践
 *
 * 这种复杂度在企业级应用中很常见，是前端开发进阶的重要参考。
 */