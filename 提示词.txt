需求文档中，courses数据库新增了一个字段status，为课程状态，"online"表示上线，"offline"表示未上线。用户只能看到和预约status为"online"的课程，请根据这个变化，修改代码。


在卡片上像”可预约“一样显示标签，让管理员看到课程的status，显示在”可预约“标签的正下方，样式与他一致。
新增选项卡，“未上线课程”，对应offline，将当前课程改为“已经上线课程”，对应online
有可能数据库里有旧的数据，旧数据没有status字段，那么就不用显示了。
管理员新添加的课程，status字段默认为offline，防止管理员误操作

点击”新增课程“按钮后
使用Popup 弹出层收集数据？


为了方便管理员编辑，把所有时间选择器的时间间隔步数改成5分钟，在日期旁边显示周几（如周一，周二，周日等）。


在这个页面，当用户点击返回键时，其实就相当于点了下面的取消按钮。现在增加一个提示，和点击取消按钮一样提示用户数据将丢失。
为管理员增加一个

考验你的时候到了。
查找所有位置
统一所有日期的数据库存储：使用 ISO 字符串，计算时，要兼容Date 对象 和 ISO 字符串

客户对“我的课程” miniprogram/pages/my-bookings页面顶部的筛选标签样式非常满意，要求本项目全部筛选标签的样式与该页面统一，注意，只改界面样式（颜色、交互）



优化结束时间的设置方式，不再要求管理员输入结束时间，而是要求管理员输入课程时长，以分钟为单位。在旁边提供三个快速选择按钮，为1h、1.5h、2h，方便管理员快速填写。
在输入数据库时，自动计算结束时间。
页面上的结束时间不可编辑，由系统自动计算。




当前页面上显示的教练名称是openid
教练的名称，仅储存在数据库users的nickName字段里，你应该用openid从users数据库中找nickName字段


以下为已实现

会员必须有会员卡才能预约，预约时，会员卡剩余次数扣减一次，扣减后不能是负数，否则不可以预约，有多张会员卡时，自动从快到期的会员卡上扣除；预约成功后，在对应bookings的cardNumber字段上，填写会员卡编号

取消预约时，读取对应bookings的cardNumber字段，若存在cardNumber字段，则找到对应会员卡，将剩余次数加1

在预约或取消预约成功后，应该提示用户操作的结果，使用Tdesign的Toast 轻提示，
https://tdesign.tencent.com/miniprogram/components/toast

把"加载中"等类似的提示也改成轻提示，保持风格统一，提示不要互相遮挡
