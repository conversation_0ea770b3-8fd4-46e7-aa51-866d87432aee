<!--notifications.wxml-->
<!--
  通知列表页面结构文件
  这是小程序的通知中心页面，负责展示用户的所有通知消息

  页面功能：
  1. 选项卡筛选：全部通知、未读通知
  2. 通知列表：显示通知标题、内容、时间、已读状态
  3. 分页加载：支持下拉刷新和上拉加载更多
  4. 交互功能：点击通知、标记已读、全部已读

  页面结构类似于：
  - 微信的消息列表页面
  - 邮箱应用的收件箱
  - 新闻应用的通知中心
-->

<!-- 根容器 -->
<view class="container">
  <!--
    页面头部区域
    包含选项卡和操作按钮
  -->
  <view class="header">
    <!--
      选项卡组件
      t-tabs: TDesign的选项卡组件

      属性说明：
      1. value: 当前选中的选项卡值，绑定到currentFilter
      2. bind:change: 选项卡切换事件，调用onTabChange方法
      3. list: 选项卡列表数据，包含label和value
      4. theme: 选项卡主题样式，line表示线条样式
      5. space-evenly: 选项卡平均分布

      选项卡数据结构：
      - label: 显示文字（"全部"、"未读"）
      - value: 选项卡值（"all"、"unread"）
    -->
    <t-tabs 
      value="{{currentFilter}}" 
      bind:change="onTabChange" 
      list="{{tabList}}"
      theme="line"
      space-evenly
    >
      <!-- 全部通知选项卡 -->
      <t-tab-panel label="全部" value="all">
        <!-- 选项卡内容在下方统一处理 -->
      </t-tab-panel>
      
      <!-- 未读通知选项卡 -->
      <t-tab-panel label="未读" value="unread">
        <!-- 选项卡内容在下方统一处理 -->
      </t-tab-panel>
    </t-tabs>

    <!--
      操作按钮区域
      只在有通知时显示操作按钮
    -->
    <view class="actions" wx:if="{{notifications.length > 0}}">
      <!--
        全部已读按钮
        t-button: TDesign的按钮组件

        属性说明：
        1. size="small": 小尺寸按钮
        2. variant="text": 文字按钮样式（无背景）
        3. theme="primary": 主题色文字
        4. bind:tap: 点击事件，调用markAllAsRead方法
      -->
      <t-button
        size="small"
        variant="text"
        theme="primary"
        bind:tap="markAllAsRead"
      >
        全部已读
      </t-button>

      <!--
        清空消息按钮
        清空所有通知消息

        属性说明：
        1. size="small": 小尺寸按钮
        2. variant="text": 文字按钮样式（无背景）
        3. theme="danger": 危险操作主题色（红色）
        4. bind:tap: 点击事件，调用clearAllNotifications方法
      -->
      <t-button
        size="small"
        variant="text"
        theme="danger"
        bind:tap="clearAllNotifications"
      >
        清空消息
      </t-button>
    </view>
  </view>

  <!--
    通知列表内容区域
    根据不同状态显示不同内容
  -->
  <view class="content">
    <!--
      加载状态显示
      当页面正在加载且没有数据时显示

      条件：loading && notifications.length === 0
      表示首次加载或刷新时的加载状态
    -->
    <view class="loading-container" wx:if="{{loading && notifications.length === 0}}">
      <!--
        加载组件
        t-loading: TDesign的加载组件

        属性说明：
        1. theme="circular": 圆形加载动画
        2. size="40rpx": 加载动画大小
        3. text: 加载提示文字
      -->
      <t-loading theme="circular" size="40rpx" text="加载中..." />
    </view>

    <!--
      空状态显示
      当没有通知时显示

      条件：!loading && notifications.length === 0
      表示加载完成但没有数据的状态
    -->
    <view class="empty-container" wx:elif="{{!loading && notifications.length === 0}}">
      <!--
        空状态组件
        t-empty: TDesign的空状态组件

        属性说明：
        1. icon: 空状态图标，使用内置的notification图标
        2. description: 空状态描述文字，根据筛选条件动态显示
      -->
      <t-empty 
        icon="notification" 
        description="{{currentFilter === 'unread' ? '暂无未读通知' : '暂无通知'}}" 
      />
    </view>

    <!--
      通知列表显示
      当有通知数据时显示

      条件：notifications.length > 0
      表示有通知数据需要展示
    -->
    <view class="notification-list" wx:else>
      <!--
        通知项列表渲染
        wx:for遍历notifications数组，为每个通知创建一个通知项

        wx:for属性说明：
        1. wx:for="{{notifications}}": 遍历通知数组
        2. wx:key="_id": 使用通知ID作为唯一标识
        3. wx:for-item="notification": 设置循环变量名为notification
      -->
      <!--
        SwipeCell 滑动操作容器
        使用TDesign的SwipeCell组件实现左滑删除效果

        t-swipe-cell属性说明：
        - right: 右侧操作按钮配置
        - bind:click: 操作按钮点击事件
      -->
      <view
        class="notification-wrapper"
        wx:for="{{notifications}}"
        wx:key="_id"
        wx:for-item="notification"
      >
        <t-swipe-cell
          bind:click="onSwipeAction"
          data-notification="{{notification}}"
          data-index="{{index}}"
        >
          <!--
            自定义右侧删除按钮
            使用slot="right"来自定义右侧操作区域
          -->
          <view slot="right" class="swipe-right-action">
            <view
              class="delete-action-btn"
              data-notification="{{notification}}"
              catchtap="onDeleteAction"
            >
              <t-icon name="delete" size="32rpx" color="#ffffff" />
              <text class="delete-text">删除</text>
            </view>
          </view>
          <!--
            通知项内容
            SwipeCell的主要内容区域
          -->
          <view
            class="notification-item {{notification.isRead ? 'read' : 'unread'}}"
            data-notification="{{notification}}"
            bind:tap="onNotificationTap"
          >
            <!--
              通知项内容结构
              采用左右布局：左侧内容，右侧时间和状态
            -->
            <view class="notification-content">
              <!--
                通知主要内容区域
                包含标题、内容文字
              -->
              <view class="notification-main">
                <!--
                  通知标题
                  显示通知的主要标题，如"预约成功"、"课程提醒"等

                  CSS类说明：
                  - notification-title: 基础标题样式
                  - unread: 未读状态的特殊样式（通常是加粗或特殊颜色）
                -->
                <view class="notification-title {{notification.isRead ? '' : 'unread'}}">
                  {{notification.title}}
                </view>

                <!--
                  通知内容
                  显示通知的详细内容文字

                  CSS类说明：
                  - notification-text: 基础内容文字样式
                  - 通常使用较小的字号和较浅的颜色
                -->
                <view class="notification-text">
                  {{notification.content}}
                </view>
              </view>

              <!--
                通知右侧信息区域
                包含时间和未读状态指示
              -->
              <view class="notification-meta">
                <!--
                  通知时间
                  显示格式化后的时间，如"刚刚"、"5分钟前"、"今天 14:30"等

                  时间格式化逻辑在JS中的formatTime方法实现
                -->
                <view class="notification-time">
                  {{notification.formattedTime}}
                </view>

                <!--
                  未读状态指示器
                  只在未读通知上显示红色圆点

                  条件渲染：wx:if="{{!notification.isRead}}"
                  只有未读通知才显示这个红点
                -->
                <view class="unread-dot" wx:if="{{!notification.isRead}}"></view>
              </view>
            </view>
          </view>
        </t-swipe-cell>
      </view>
    </view>

    <!--
      底部加载更多状态
      当正在加载更多数据时显示

      条件：loading && notifications.length > 0
      表示在已有数据基础上加载更多数据的状态
    -->
    <view class="load-more" wx:if="{{loading && notifications.length > 0}}">
      <t-loading theme="circular" size="32rpx" text="加载更多..." />
    </view>

    <!--
      没有更多数据提示
      当已加载所有数据时显示

      条件：!hasMore && notifications.length > 0
      表示已经加载完所有数据，没有更多内容了
    -->
    <view class="no-more" wx:if="{{!hasMore && notifications.length > 0}}">
      <view class="no-more-text">没有更多通知了</view>
    </view>
  </view>

  <!--
    测试功能提示
    提醒用户当前通知功能为测试版本

    设计说明：
    - 固定在页面底部显示
    - 使用较小的字体和浅色，不干扰主要内容
    - 提醒用户以实际预约结果为准
  -->
  <view class="test-notice">
    <!--
      提示图标
      使用信息图标增强视觉效果

      t-icon组件属性：
      - name="info-circle": 信息圆圈图标
      - size="24rpx": 小尺寸图标
      - color="#999999": 浅灰色，与文字颜色保持一致
    -->
    <t-icon name="info-circle" size="24rpx" color="#999999" />

    <!--
      提示文字
      说明当前功能的测试状态和注意事项
    -->
    <text class="test-notice-text">
      消息通知目前是测试功能，可能有漏发、错发消息的情况，请以实际预约结果为准。
    </text>
  </view>

  <!--
    通知详情弹窗
    点击通知时显示完整的通知内容

    t-popup: TDesign的弹窗组件
    属性说明：
    1. visible: 控制弹窗显示/隐藏状态
    2. bind:visible-change: 弹窗状态改变时的回调
    3. placement="center": 弹窗位置居中显示
    4. show-overlay: 显示遮罩层
    5. close-on-overlay-click: 点击遮罩层关闭弹窗
  -->
  <t-popup
    visible="{{showDetailPopup}}"
    bind:visible-change="onDetailPopupChange"
    placement="center"
    show-overlay
    close-on-overlay-click
  >
    <!--
      弹窗内容容器
      包含通知的完整详细信息
    -->
    <view class="notification-detail-popup">
      <!--
        弹窗头部
        包含标题和关闭按钮
      -->
      <view class="popup-header">
        <!--
          弹窗标题
          显示"消息详情"
        -->
        <view class="popup-title">消息详情</view>

        <!--
          关闭按钮
          点击关闭弹窗
        -->
        <view class="popup-close" bind:tap="closeDetailPopup">
          <t-icon name="close" size="32rpx" color="#666666" />
        </view>
      </view>

      <!--
        弹窗内容区域
        显示通知的详细信息
      -->
      <view class="popup-content" wx:if="{{selectedNotification}}">
        <!--
          通知标题
          显示通知的主标题
        -->
        <view class="detail-title">
          {{selectedNotification.title}}
        </view>

        <!--
          通知时间
          显示通知的创建时间
        -->
        <view class="detail-time">
          {{selectedNotification.formattedTime}}
        </view>

        <!--
          通知内容
          显示通知的完整内容文字
        -->
        <view class="detail-content">
          {{selectedNotification.content}}
        </view>
      </view>

      <!--
        弹窗底部操作区域
        包含相关操作按钮
      -->
      <view class="popup-actions" wx:if="{{selectedNotification}}">
        <!--
          删除按钮
          删除当前通知
        -->
        <t-button
          theme="danger"
          variant="outline"
          size="small"
          bind:tap="deleteSelectedNotification"
        >
          删除消息
        </t-button>

        <!--
          关闭按钮
          关闭弹窗
        -->
        <t-button
          theme="default"
          size="small"
          bind:tap="closeDetailPopup"
        >
          关闭
        </t-button>
      </view>
    </view>
  </t-popup>

  <!--
    Toast消息提示组件
    用于显示操作结果的提示信息

    t-toast: TDesign的消息提示组件
    id="t-toast": 设置组件ID，方便在JS中调用
  -->
  <t-toast id="t-toast" />
</view>
