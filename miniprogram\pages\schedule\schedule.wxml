<!--schedule.wxml-->
<!--
  课程表页面结构文件
  这是小程序的课程表页面，负责展示活动列表和预约功能

  页面功能：
  1. 视图切换：按日筛选、当前活动、历史活动三种视图
  2. 搜索功能：支持按课程名称、讲师姓名、场地搜索
  3. 活动列表：展示活动详情、预约状态、操作按钮
  4. 预约管理：支持预约、取消预约操作
  5. 分页加载：历史活动支持懒加载优化性能

  页面设计模式：
  - Tab切换 + 搜索 + 列表的经典布局
  - 类似于电商应用的商品列表页面
  - 类似于社交应用的动态列表页面
-->

<!-- 根容器 -->
<view class="container">
  <!--
    视图切换栏区域
    提供三种不同的数据视图切换功能
    使用TDesign的t-tabs组件，与course-management页面保持一致的美观样式
  -->
  <view class="view-section">
    <!--
      顶部选项卡区域容器
      使用与course-management页面相同的样式设计
    -->
    <view class="top-tabs-section">
      <!--
        TDesign线条选项卡组件

        属性说明：
        - value: 当前激活的选项卡值，对应activeView数据
        - bindchange: 选项卡切换事件，调用onViewChange方法
        - theme: 选项卡主题，"line"表示线条式选项卡
        - show-bottom-line: 是否显示底部分割线
        - t-class: 自定义样式类名，使用custom-top-tabs样式

        与原来的booking-tabs对比：
        - 原来：自定义div + CSS实现的简单tab
        - 现在：TDesign专业组件，样式更美观，交互更流畅

        与course-management页面的区别：
        - course-management: "活动维护"、"模板维护"
        - schedule: "活动表"、"当前活动"、"历史活动"
      -->
      <t-tabs value="{{activeView}}" bindchange="onViewChange" theme="line" show-bottom-line="{{true}}" t-class="custom-top-tabs">
        <!--
          选项卡面板：定义具体的tab项
          t-tab-panel: TDesign的选项卡面板组件

          使用wx:for循环渲染viewTabs数组中的每个选项
          这样可以保持数据驱动的设计模式
        -->
        <t-tab-panel
          wx:for="{{viewTabs}}"
          wx:key="value"
          value="{{item.value}}"
          label="{{item.label}}"
        />
      </t-tabs>
    </view>

    <!--
      搜索框区域（条件显示）
      只在"当前活动"视图下显示搜索功能
      使用与course-management页面相同的搜索框样式，但不使用折叠功能
    -->
    <view wx:if="{{activeView === 'current'}}" class="search-section">
      <!--
        搜索区域容器
        使用与course-management页面相同的样式设计
        但直接显示展开状态，不需要折叠功能
      -->
      <view class="search-actions-section expanded">
        <!--
          展开状态布局：搜索框占据全部宽度
          与course-management页面的expanded-layout保持一致
        -->
        <view class="expanded-layout">
          <!--
            搜索输入框容器
            包含搜索图标、输入框和清空图标的容器

            功能说明：
            - 搜索图标：视觉提示，表明这是搜索功能
            - 输入框：用户输入搜索关键词
            - 清空图标：当有输入内容时显示，点击清空搜索

            与原来的t-search组件对比：
            - 原来：TDesign的t-search组件，功能完整但样式固定
            - 现在：自定义搜索框，样式与course-management保持一致
          -->
          <view class="search-input-container">
            <!--
              搜索图标
              使用TDesign的图标组件，提供视觉提示
            -->
            <t-icon name="search" size="16" class="search-icon" />

            <!--
              搜索输入框
              原生input组件，提供更好的控制能力

              属性说明：
              - placeholder: 占位符文字，提示用户可以搜索的内容
              - value: 双向数据绑定搜索值
              - bindinput: 输入内容改变时触发（实时搜索）
              - bindconfirm: 提交搜索时触发（点击搜索按钮或回车）
            -->
            <input
              class="search-input"
              placeholder="搜索活动名称/讲师/场地"
              value="{{searchValue}}"
              bindinput="onSearchInput"
              bindconfirm="onSearchSubmit"
            />

            <!--
              清空搜索图标
              条件显示：只有当有搜索内容时才显示
              点击后清空搜索内容并重新加载数据
            -->
            <t-icon
              wx:if="{{searchValue}}"
              name="close-circle-filled"
              size="16"
              class="clear-icon"
              bindtap="onSearchClear"
            />
          </view>
        </view>
      </view>
    </view>

  </view> 


  <!-- 横向日期选择器 - 仅在按日筛选视图下显示 -->
  <scroll-view wx:if="{{activeView === 'byDate'}}" class="date-tabs-scroll" scroll-x="true">
    <view class="date-tab"
          wx:for="{{dateTabs}}"
          wx:key="value"
          data-value="{{item.value}}"
          bindtap="onDateTabChange"
          style="color:{{selectedDate === item.value ? '#222' : '#bbb'}};font-weight:{{selectedDate === item.value ? 'bold' : 'normal'}}">
      <view class="tab-label">{{item.label}}</view>
      <view class="tab-date">{{item.date}}</view>
      <view wx:if="{{selectedDate === item.value}}" style="height:3px;background:#0052d9;border-radius:2px;margin-top:2px;"></view>
    </view>
  </scroll-view>

  <!-- 活动列表 -->
  <view class="course-list">
    <t-empty wx:if="{{activeView === 'history' && historyCourses.length === 0}}" description="{{emptyDescription}}" />
    <!-- 使用原生滚动替代scroll-view，解决滚动响应性问题 -->
    <view
      wx:if="{{activeView === 'history' && historyCourses.length > 0}}"
      class="course-list"
      bindscrolltolower="onReachBottom"
    >
      <block wx:for="{{historyCourses}}" wx:key="id">
        <!-- 日期分组分隔条 -->
        <view wx:if="{{index === 0 || historyCourses[index-1].date !== item.date}}" class="timeline-date">{{item.date}}</view>
        <view class="course-card" bind:tap="goToCourseDetail" data-course="{{item}}">
          <view class="course-header">
            <view class="course-title">{{item.name}}</view>
            <view class="course-status ended">已结束</view>
          </view>
          <view class="course-info-list">
            <view class="info-item">
              <t-icon name="time" size="16" />
              <text>{{item.formattedDate}} {{item.time}}</text>
            </view>
            <view class="info-item">
              <t-icon name="user" size="16" />
              <text>{{item.coach}}</text>
            </view>
            <view class="info-item">
              <t-icon name="location" size="16" />
              <text>{{item.venue}}</text>
            </view>
            <view class="info-item">
              <t-icon name="chart" size="16" />
              <text>剩余名额：{{item.remaining}}/{{item.capacity}}</text>
            </view>
          </view>
        </view>
      </block>
      <!-- 加载状态指示器 -->
      <view wx:if="{{historyLoading}}" class="loading-indicator">
        <view class="loading-dots">
          <view class="loading-dot"></view>
          <view class="loading-dot"></view>
          <view class="loading-dot"></view>
        </view>
        <text>正在加载历史活动...</text>
      </view>
      <view wx:elif="{{!historyHasMore}}" class="end-indicator">没有更多了</view>
      <view wx:else class="end-indicator">上拉加载更多</view>
    </view>
    <!-- 当前活动视图 - 时间轴分页模式 -->
    <view wx:elif="{{activeView === 'current'}}" style="flex: 1; display: flex; flex-direction: column;">
      <t-empty wx:if="{{visibleCurrentCourses.length === 0}}" description="{{emptyDescription}}" />
      <!-- 使用原生滚动替代scroll-view，解决滚动响应性问题 -->
      <view
        wx:else
        class="course-list"
        bindscrolltolower="onCurrentScrollToLower"
      >
        <block wx:for="{{visibleCurrentCourses}}" wx:key="id">
          <!-- 时间轴分组：如果是新日期，显示日期分隔 -->
          <view wx:if="{{index === 0 || visibleCurrentCourses[index-1].timelineDate !== item.timelineDate}}" class="timeline-date">{{item.timelineDate}}</view>
          <view class="course-card{{flashCurrentIndexes.indexOf(index) !== -1 ? ' slide-in' : ''}}" bind:tap="goToCourseDetail" data-course="{{item}}">
            <view class="course-header">
              <view class="course-title">{{item.name}}</view>
              <!-- 优化状态显示逻辑 - 添加进行中状态 -->
              <view class="course-status {{item.ended ? 'ended' : (item.inProgress ? 'in-progress' : (item.isBooked ? 'booked' : (item.available ? 'available' : 'full')))}}">
                {{ item.ended ? '已结束' : (item.inProgress ? '进行中' : (item.isBooked ? '已预约' : (item.available ? '可预约' : '已满'))) }}
              </view>
            </view>
            <view class="course-info-list">
              <view class="info-item">
                <t-icon name="time" size="16" />
                <text>{{item.formattedDate}} {{item.time}}</text>
              </view>
              <view class="info-item">
                <t-icon name="user" size="16" />
                <text>{{item.coach}}</text>
              </view>
              <view class="info-item">
                <t-icon name="location" size="16" />
                <text>{{item.venue}}</text>
              </view>
              <view class="info-item">
                <t-icon name="chart" size="16" />
                <text>剩余名额：{{item.remaining}}/{{item.capacity}}</text>
              </view>
            </view>
            <view class="course-footer">
              <view class="action-buttons">
                <!-- 已结束：显示禁用按钮 -->
                <t-button
                  wx:if="{{item.ended}}"
                  size="small"
                  theme="default"
                  disabled
                >
                  已结束
                </t-button>
                <!-- 进行中：显示禁用按钮 -->
                <t-button
                  wx:elif="{{item.inProgress}}"
                  size="small"
                  theme="default"
                  disabled
                >
                  进行中
                </t-button>
                <!-- 已预约且未开始：显示取消按钮 -->
                <t-button
                  wx:elif="{{item.isBooked}}"
                  size="small"
                  theme="danger"
                  catch:tap="cancelBooking"
                  data-course="{{item}}"
                >
                  取消预约
                </t-button>
                <!-- 可预约状态：显示预约按钮 -->
                <t-button
                  wx:elif="{{item.available}}"
                  size="small"
                  theme="primary"
                  catch:tap="bookCourse"
                  data-course="{{item}}"
                >
                  预约
                </t-button>
                <!-- 已满状态：显示禁用按钮 -->
                <t-button
                  wx:elif="{{item.isFull}}"
                  size="small"
                  theme="default"
                  disabled
                >
                  已满
                </t-button>
                <!-- 其他情况：未知状态 -->
                <t-button
                  wx:else
                  size="small"
                  theme="default"
                  disabled
                >
                  未知
                </t-button>
              </view>
            </view>
          </view>
        </block>
        <!-- 加载状态指示器 -->
        <view wx:if="{{isLoadingCurrentBottom}}" class="loading-indicator">
          <view class="loading-dots">
            <view class="loading-dot"></view>
            <view class="loading-dot"></view>
            <view class="loading-dot"></view>
          </view>
          <text>正在加载更多活动...</text>
        </view>
        <view wx:elif="{{!noMoreCurrent}}" class="end-indicator">上拉加载更多</view>
        <view wx:else class="end-indicator">没有更多了</view>
      </view>
    </view>

    <!-- 其他视图保持原有渲染 -->
    <view wx:elif="{{activeView !== 'history' && activeView !== 'current'}}" style="flex: 1; display: flex; flex-direction: column;">
      <t-empty wx:if="{{filteredCourseList.length === 0}}" description="{{emptyDescription}}" />
      <!-- 使用原生滚动替代scroll-view，解决滚动响应性问题 -->
      <view
        wx:else
        class="course-list"
      >
        <view
          class="course-card"
          wx:for="{{filteredCourseList}}"
          wx:key="id"
          bind:tap="goToCourseDetail"
          data-course="{{item}}"
        >
          <view class="course-header">
            <view class="course-title">{{item.name}}</view>
            <!-- 优化状态显示逻辑 - 添加进行中状态 -->
            <view class="course-status {{item.ended ? 'ended' : (item.inProgress ? 'in-progress' : (item.isBooked ? 'booked' : (item.available ? 'available' : 'full')))}}">
              {{ item.ended ? '已结束' : (item.inProgress ? '进行中' : (item.isBooked ? '已预约' : (item.available ? '可预约' : '已满'))) }}
            </view>
          </view>
          <view class="course-info-list">
            <view class="info-item">
              <t-icon name="time" size="16" />
              <text>{{item.formattedDate}} {{item.time}}</text>
            </view>
            <view class="info-item">
              <t-icon name="user" size="16" />
              <text>{{item.coach}}</text>
            </view>
            <view class="info-item">
              <t-icon name="location" size="16" />
              <text>{{item.venue}}</text>
            </view>
            <view class="info-item">
              <t-icon name="chart" size="16" />
              <text>剩余名额：{{item.remaining}}/{{item.capacity}}</text>
            </view>
          </view>
          <view class="course-footer">
            <view class="action-buttons">
              <!-- 已结束：显示禁用按钮 -->
              <t-button
                wx:if="{{item.ended}}"
                size="small"
                theme="default"
                disabled
              >
                已结束
              </t-button>
              <!-- 进行中：显示禁用按钮 -->
              <t-button
                wx:elif="{{item.inProgress}}"
                size="small"
                theme="default"
                disabled
              >
                进行中
              </t-button>
              <!-- 已预约且未开始：显示取消按钮 -->
              <t-button
                wx:elif="{{item.isBooked}}"
                size="small"
                theme="danger"
                catch:tap="cancelBooking"
                data-course="{{item}}"
              >
                取消预约
              </t-button>
              <!-- 可预约状态：显示预约按钮 -->
              <t-button
                wx:elif="{{item.available}}"
                size="small"
                theme="primary"
                catch:tap="bookCourse"
                data-course="{{item}}"
              >
                预约
              </t-button>
              <!-- 已满状态：显示禁用按钮 -->
              <t-button
                wx:elif="{{item.isFull}}"
                size="small"
                theme="default"
                disabled
              >
                已满
              </t-button>
              <!-- 其他情况：未知状态 -->
              <t-button
                wx:else
                size="small"
                theme="default"
                disabled
              >
                未知
              </t-button>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
<!-- 移除自定义弹窗，使用系统默认弹窗 -->
<t-toast id="t-toast" />
</view> 
