 <!-- 页面标题 -->
<view class="page-header">
  <view class="page-title">
    {{isEdit ? (mode === 'template' ? '编辑模板' : '编辑活动') : (mode === 'template' ? '新建模板' : '新建活动')}}
  </view>
</view>

<!-- 模板选择区域 -->
<view class="top-actions" wx:if="{{!isEdit && mode === 'course'}}">
  <t-button theme="primary" size="small" bindtap="onChooseTemplate" class="template-btn">
    <t-icon name="layers" slot="prefix" />
    选择活动模板
  </t-button>
</view>

<!-- 课程模板选择 Picker -->
<t-picker
  visible="{{templatePickerVisible}}"
  value="{{templatePickerValue}}"
  data-key="template"
  title="选择活动模板"
  cancelBtn="取消"
  confirmBtn="确认"
  usingCustomNavbar
  bindchange="onPickerChange"
  bindpick="onColumnChange"
  bindcancel="onPickerCancel"
  bindconfirm="onTemplatePickerConfirm"
>
  <t-picker-item options="{{templatePickerOptions[0]}}"></t-picker-item>
</t-picker>

<!-- 表单区域 -->
<view class="form-area">
  <!-- 基础信息区域 -->
  <view class="form-section">
    
    <view class="form-item">
      <text class="label required">活动名称</text>
      <t-input 
        placeholder="请输入活动名称" 
        value="{{course.name}}" 
        bindchange="onInputChange" 
        data-field="name"
        maxlength="50"
      />
      <text class="error-text" wx:if="{{formErrors.name}}">{{formErrors.name}}</text>
    </view>
    
    <view class="form-item">
      <text class="label required">讲师</text>

      <!-- 讲师选择区域 - 带头像显示 -->
      <view class="coach-selector" bind:tap="onCoachPicker">
        <!-- 已选择讲师时显示头像和信息 -->
        <view class="coach-info" wx:if="{{selectedCoach}}">
          <view class="coach-avatar-container">
            <!-- 如果有头像则显示头像，否则显示默认图标 -->
            <image
              wx:if="{{selectedCoach.avatar}}"
              class="coach-avatar"
              src="{{selectedCoach.avatar}}"
              mode="aspectFill"
            />
            <!-- 默认头像图标 -->
            <view wx:else class="default-avatar-icon">
              <t-icon name="user" size="32" color="#999" />
            </view>
          </view>
          <view class="coach-details">
            <text class="coach-name">{{selectedCoach.name}}</text>
            <text class="coach-title">{{selectedCoach.title || '讲师'}}</text>
          </view>
        </view>

        <!-- 未选择讲师时的占位符 -->
        <view class="coach-placeholder" wx:else>
          <view class="placeholder-icon">
            <t-icon name="user" size="24" color="#999" />
          </view>
          <text class="placeholder-text">请选择讲师</text>
        </view>

        <!-- 右侧箭头 -->
        <view class="coach-arrow">
          <t-icon name="chevron-right" size="16" color="#999" />
        </view>
      </view>

      <text class="error-text" wx:if="{{formErrors.coach}}">{{formErrors.coach}}</text>
    </view>
    <view class="form-item" wx:if="{{mode === 'course'}}">
      <text class="label required">开始时间</text>
      <t-cell
        note="{{startTimeText}}"
        t-class="panel-item"
        bindtap="showDateTimePicker"
        data-mode="startTime"
        arrow
      />
      <t-date-time-picker
        visible="{{startTimeVisible}}"
        value="{{startTime}}"
        mode="datetime"
        bindconfirm="onStartTimeConfirm"
        bindcancel="hideStartTimePicker"
        bindcolumnchange="onStartTimeColumnChange"
      />

      <text class="error-text" wx:if="{{formErrors.startTime}}">{{formErrors.startTime}}</text>
    </view>
    <view class="form-item">
      <text class="label required">活动时长（分钟）</text>
      <t-input 
        type="number" 
        placeholder="请输入活动时长（分钟）" 
        value="{{course.duration}}" 
        bindchange="onInputChange" 
        data-field="duration" 
      />
      <view class="quick-buttons">
        <t-button 
          size="small" 
          theme="light" 
          bindtap="setDuration" 
          data-duration="60"
          class="quick-btn"
        >
          1h
        </t-button>
        <t-button 
          size="small" 
          theme="light" 
          bindtap="setDuration" 
          data-duration="90"
          class="quick-btn"
        >
          1.5h
        </t-button>
        <t-button 
          size="small" 
          theme="light" 
          bindtap="setDuration" 
          data-duration="120"
          class="quick-btn"
        >
          2h
        </t-button>
      </view>
      <text class="error-text" wx:if="{{formErrors.duration}}">{{formErrors.duration}}</text>
    </view>
    <view class="form-item" wx:if="{{mode === 'course'}}">
      <text class="label">结束时间</text>
      <t-cell
        note="{{endTimeText}}"
        t-class="panel-item"
        disabled
      />
      <text class="error-text" wx:if="{{formErrors.endTime}}">{{formErrors.endTime}}</text>
    </view>
    
  
    
    
    <view class="form-item">
      <text class="label required">活动地点</text>
      <t-input 
        placeholder="请输入活动地点" 
        value="{{course.venue}}" 
        bindchange="onInputChange" 
        data-field="venue"
        maxlength="100"
      />
      <text class="error-text" wx:if="{{formErrors.venue}}">{{formErrors.venue}}</text>
    </view>
    
    <view class="form-item">
      <text class="label required">人数上限</text>
      <t-input 
        type="number" 
        placeholder="请输入人数上限（1-999）" 
        value="{{course.capacity}}" 
        bindchange="onInputChange" 
        data-field="capacity" 
      />
      <text class="error-text" wx:if="{{formErrors.capacity}}">{{formErrors.capacity}}</text>
    </view>
  </view>

  <!-- 课程详情区域 -->
  <view class="form-section">

    <view class="form-item">
      <text class="label required">活动详情</text>
      <t-textarea
        placeholder="请输入活动详情"
        value="{{course.activityDetail}}"
        bindchange="onInputChange"
        data-field="activityDetail"
        maxlength="500"
        autosize="{{ { minRows: 3, maxRows: 8 } }}"
        indicator
      />
      <text class="error-text" wx:if="{{formErrors.activityDetail}}">{{formErrors.activityDetail}}</text>
    </view>
  </view>

  <!-- 课程图片区域 -->
  <view class="form-section">
    <view class="form-item">
      <text class="label">活动图片</text>
      <text class="label-desc">最多可上传9张图片，用于活动详情页展示</text>

      <!-- 图片网格 -->
      <view class="image-grid">
        <!-- 已上传的图片 -->
        <view
          class="image-item"
          wx:for="{{courseImages}}"
          wx:key="fileID"
          bindtap="onPreviewImage"
          data-index="{{index}}"
        >
          <image
            src="{{item.tempFileURL}}"
            mode="aspectFill"
            class="course-image"
          />
          <view
            class="image-delete-btn"
            catchtap="onDeleteImage"
            data-index="{{index}}"
          >
            <t-icon name="close" size="16" color="#fff" />
          </view>
        </view>

        <!-- 添加图片按钮 -->
        <view
          class="image-add-btn"
          wx:if="{{courseImages.length < 9}}"
          bindtap="onChooseImage"
        >
          <t-icon name="add" size="32" color="#999" />
          <text class="add-text">添加图片</text>
        </view>
      </view>

      <!-- 上传状态提示 -->
      <view class="upload-status" wx:if="{{isUploadingImage}}">
        <t-loading size="16" />
        <text class="upload-text">上传中...</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮区域 -->
  <view class="action-area">
    <t-button 
      theme="primary" 
      bindtap="onSubmit" 
      loading="{{isSubmitting}}"
      disabled="{{isSubmitting}}"
    >
      {{isSubmitting ? '保存中...' : '保存'}}
    </t-button>
    
    <t-button 
      theme="default" 
      bindtap="onSaveAsTemplate" 
      loading="{{isSubmitting}}"
      disabled="{{isSubmitting}}"
      wx:if="{{mode === 'course'}}"
    >
      保存为模板
    </t-button>
    
    <t-button 
      theme="default" 
      bindtap="onCancel"
      disabled="{{isSubmitting}}"
    >
      取消
    </t-button>
  </view>
</view>

<!-- 讲师选择器 -->
<t-picker
  visible="{{coachVisible}}"
  value="{{coachValue}}"
  data-key="coach"
  title="选择讲师"
  cancelBtn="取消"
  confirmBtn="确认"
  usingCustomNavbar
  bindchange="onPickerChange"
  bindpick="onColumnChange"
  bindcancel="onPickerCancel"
  bindconfirm="onCoachPickerConfirm"
>
  <t-picker-item options="{{coachOptions}}"></t-picker-item>
</t-picker>

<!-- 开始时间选择器 -->
<t-date-time-picker
  title="选择开始时间"
  visible="{{startTimeVisible}}"
  mode="minute"
  value="{{startTime}}"
  format="YYYY-MM-DD HH:mm"
  show-week="{{true}}"
  bindchange="onStartTimeConfirm"
  bindpick="onStartTimeColumnChange"
  bindcancel="hideStartTimePicker"
/>
<t-toast id="t-toast" />

