<!--pages/user-management/user-management.wxml-->
<view class="container">
  <!-- 顶部选项卡区域 -->
  <view class="top-section">
    <view class="top-tabs-section">
      <t-tabs value="{{activeTab}}" bindchange="onTabChange" theme="line" show-bottom-line="{{true}}" t-class="custom-top-tabs">
        <t-tab-panel value="all" label="全部用户" />
        <t-tab-panel value="student" label="学员" />
        <t-tab-panel value="coach" label="讲师" />
        <t-tab-panel value="admin" label="管理员" />
      </t-tabs>
    </view>
    
    <!-- 搜索栏 -->
    <view class="search-actions-section expanded">
      <view class="expanded-layout">
        <view class="search-input-container">
          <t-icon name="search" size="16" class="search-icon" />
          <input 
            class="search-input"
            placeholder="搜索用户昵称/ID"
            value="{{searchValue}}"
            bindinput="onSearchInput"
            bindconfirm="onSearchSubmit"
          />
          <t-icon 
            wx:if="{{searchValue}}" 
            name="close-circle-filled" 
            size="16" 
            class="clear-icon"
            bindtap="onSearchClear"
          />
        </view>
      </view>
    </view>
  </view>

  <!-- 用户列表内容 -->
  <view class="user-content">
    <!-- 用户列表 -->
    <view class="user-list">
      <view wx:if="{{loading}}" class="loading-indicator">加载中...</view>
      <t-empty wx:elif="{{!loading && filteredUserList.length === 0}}" description="{{searchValue ? '未找到匹配的用户' : '暂无用户数据'}}" />
      <scroll-view 
        wx:elif="{{!loading && filteredUserList.length > 0}}"
        scroll-y="true"
        style="flex: 1; min-height: 0;"
        bindscrolltolower="onReachBottom"
      >
        <block wx:for="{{filteredUserList}}" wx:key="_id">
          <view class="user-card" bindtap="onUserCardTap" data-user="{{item}}">
            <view class="user-header">
              <view class="user-avatar">
                <t-avatar 
                  image="{{item.avatarUrl}}" 
                  size="48px" 
                  shape="circle"
                  bind:error="onAvatarError"
                  data-index="{{index}}"
                />
              </view>
              <view class="user-info">
                <view class="user-title">{{item.nickName || '未知用户'}}</view>
                <view class="user-roles">
                  <block wx:if="{{item.roles && item.roles.length > 0}}">
                    <t-tag 
                      wx:for="{{item.roles}}" 
                      wx:for-item="roleName" 
                      wx:key="roleName"
                      theme="primary"
                      variant="light"
                      size="small"
                      class="role-tag"
                    >
                      {{roleName}}
                    </t-tag>
                  </block>
                  <block wx:elif="{{item.role}}">
                    <t-tag 
                      theme="primary"
                      variant="light"
                      size="small"
                      class="role-tag"
                    >
                      {{item.role}}
                    </t-tag>
                  </block>
                  <block wx:else>
                    <t-tag 
                      theme="primary"
                      variant="light"
                      size="small"
                      class="role-tag"
                    >
                      未知角色
                    </t-tag>
                  </block>
                </view>
              </view>
            </view>
            
            <view class="user-info-list">
              <view class="info-item">
                <t-icon name="user" size="16" />
                <text class="info-label">用户ID：</text>
                <text class="copyable-text" catchtap="onCopyOpenid" data-openid="{{item.openid}}">{{item.openid}}</text>
              </view>
              <view class="info-item">
                <t-icon name="time" size="16" />
                <text class="info-label">注册时间：</text>
                <text>{{item.createTime}}</text>
              </view>
              <view class="info-item">
                <t-icon name="time" size="16" />
                <text class="info-label">最后登录：</text>
                <text>{{item.lastLoginTime}}</text>
              </view>
            </view>
            
            <view class="user-footer">
              <view class="action-buttons">
                <t-button size="small" theme="primary" catchtap="onEditRole" data-id="{{item._id}}">
                  <t-icon name="edit" slot="prefix" />
                  改角色
                </t-button>
                <t-button 
                  wx:if="{{item.isCoach}}"
                  size="small" 
                  theme="warning" 
                  catchtap="onEditCoachInfo" 
                  data-user="{{item}}"
                >
                  <t-icon name="user" slot="prefix" />
                  讲师信息
                </t-button>
                <t-button size="small" theme="danger" catchtap="onDeleteUser" data-user="{{item}}">
                  <t-icon name="delete" slot="prefix" />
                  删除
                </t-button>
              </view>
            </view>
          </view>
        </block>
        <view wx:if="{{loading}}" class="loading-indicator">加载中...</view>
        <view wx:elif="{{!userHasMore}}" class="end-indicator">没有更多了</view>
      </scroll-view>
    </view>
  </view>

  <!-- 操作确认弹窗/提示 -->
  <t-dialog id="confirmDialog" />
  <t-message id="message" />
  <t-toast id="t-toast" />
  
  <!-- 删除用户确认对话框 -->
  <t-dialog
    visible="{{deleteDialogVisible}}"
    title="⚠️ 删除用户确认"
    content="{{deleteDialogContent}}"
    confirm-btn="{{deleteDialogConfirmBtn}}"
    cancel-btn="取消"
    bind:confirm="onDeleteConfirm"
    bind:cancel="onDeleteCancel"
    bind:close="onDeleteCancel"
  />
  
  <!-- 改角色弹窗 -->
  <t-popup
    visible="{{editRolePopupVisible}}"
    placement="center"
    bind:visible-change="onEditRoleCancel"
  >
    <view class="edit-role-popup">
      <view class="popup-header">
        <text class="popup-title">修改用户角色</text>
        <t-icon name="close" size="20" bind:tap="onEditRoleCancel" class="close-icon" />
      </view>
      
      <view class="popup-content">
        <view class="user-info">
          <text class="user-name">{{editRoleUserData.nickName || '未知用户'}}</text>
          <text class="user-id">ID: {{editRoleUserData._id}}</text>
        </view>
        
        <view class="role-section">
          <text class="section-title">选择角色</text>
          <text class="section-tip">* 学员身份默认拥有且不可修改</text>
          <text class="section-tip">* 管理员身份只能由开发者在数据库中修改</text>
        </view>
        
        <view class="role-checkboxes">
          <t-checkbox-group 
            value="{{selectedRoleValues}}"
            bind:change="onRoleCheckboxChange"
          >
            <view 
              wx:for="{{roleCheckboxes}}" 
              wx:key="value" 
              class="role-checkbox-item"
            >
              <t-checkbox 
                value="{{item.value}}" 
                checked="{{item.checked}}"
                disabled="{{item.disabled}}"
                label="{{item.label}}"
                icon="rectangle"
              />
            </view>
          </t-checkbox-group>
        </view>
      </view>
      
      <view class="popup-footer">
        <t-button theme="light" bind:tap="onEditRoleCancel">取消</t-button>
        <t-button theme="primary" bind:tap="onEditRoleConfirm">确认</t-button>
      </view>
    </view>
  </t-popup>
  
  <!-- 讲师信息编辑弹窗 -->
  <t-popup
    visible="{{coachInfoPopupVisible}}"
    placement="center"
    bind:visible-change="onCoachInfoCancel"
  >
    <view class="coach-info-popup">
      <view class="popup-header">
        <text class="popup-title">编辑讲师信息</text>
        <t-icon name="close" size="20" bind:tap="onCoachInfoCancel" class="close-icon" />
      </view>
      
      <view class="popup-content">
        <view class="user-info">
          <text class="user-name">{{coachInfoUserData.nickName || '未知用户'}}</text>
          <text class="user-id">ID: {{coachInfoUserData._id}}</text>
        </view>
        
        <view class="form-section">
          <view class="form-item">
            <text class="form-label">讲师简介</text>
            <t-textarea 
              placeholder="请输入讲师简介" 
              value="{{coachInfo.introduction}}"
              bindchange="onCoachInfoChange"
              data-field="introduction"
              maxlength="500"
              autosize="{{ { minRows: 3, maxRows: 8 } }}"
              style="width: 100%;"
            />
          </view>
          
          <view class="form-item">
            <text class="form-label">专长领域</text>
            <t-textarea 
              placeholder="请输入专长领域，多个专长用逗号分隔" 
              value="{{coachInfo.specialties}}"
              bindchange="onCoachInfoChange"
              data-field="specialties"
              maxlength="200"
              autosize="{{ { minRows: 2, maxRows: 4 } }}"
              style="width: 100%;"
            />
          </view>
        </view>
      </view>
      
      <view class="popup-footer">
        <t-button theme="light" bind:tap="onCoachInfoCancel">取消</t-button>
        <t-button theme="primary" bind:tap="onCoachInfoConfirm">保存</t-button>
      </view>
    </view>
  </t-popup>
</view>