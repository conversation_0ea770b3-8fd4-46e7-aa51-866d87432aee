// systemSettings.js
// 系统设置相关工具函数

/**
 * 加载联系信息
 * @returns {Promise<Object>} 联系信息对象
 */
export async function loadContactInfo() {
  try {
    const result = await wx.cloud.callFunction({
      name: 'adminManagement',
      data: {
        action: 'getSystemSettings'
      }
    });
    
    if (result.result.success) {
      const settings = result.result.data;
      const contactInfo = {
        phone: settings.contact?.phone || '',
        address: settings.contact?.address || '',
        announcement: settings.contact?.announcement || ''
      };
      
      console.log('从数据库获取的联系信息:', contactInfo);
      return contactInfo;
    } else {
      console.error('获取系统设置失败:', result.result.message);
      return null;
    }
  } catch (error) {
    console.error('加载联系信息失败:', error);
    return null;
  }
}

/**
 * 获取系统设置
 * @returns {Promise<Object>} 系统设置对象
 */
export async function getSystemSettings() {
  try {
    const result = await wx.cloud.callFunction({
      name: 'adminManagement',
      data: {
        action: 'getSystemSettings'
      }
    });
    
    if (result.result.success) {
      return result.result.data;
    } else {
      throw new Error(result.result.message);
    }
  } catch (error) {
    console.error('获取系统设置失败:', error);
    throw error;
  }
}

/**
 * 更新系统设置
 * @param {Object} settings - 系统设置对象
 * @returns {Promise<boolean>} 是否更新成功
 */
export async function updateSystemSettings(settings) {
  try {
    const result = await wx.cloud.callFunction({
      name: 'adminManagement',
      data: {
        action: 'updateSystemSettings',
        settings: settings
      }
    });
    
    if (result.result.success) {
      return true;
    } else {
      throw new Error(result.result.message);
    }
  } catch (error) {
    console.error('更新系统设置失败:', error);
    throw error;
  }
}