// notifications.js
// 通知列表页面 - 显示用户的所有通知消息

import { showToast, hideToast, showLoading, showSuccess, showError, showWarning } from '../../utils/toast.js';

Page({
  /**
   * 页面的初始数据
   * 
   * 数据结构说明：
   * - notifications: 通知列表数组
   * - currentFilter: 当前筛选条件（'all'全部, 'unread'未读）
   * - loading: 页面加载状态
   * - hasMore: 是否还有更多数据
   * - page: 当前页码
   * - pageSize: 每页数量
   */
  data: {
    // 通知列表数据
    notifications: [],
    
    // 筛选和分页相关
    currentFilter: 'all', // 'all' | 'unread'
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 20,
    
    // 选项卡配置
    tabList: [
      { label: '全部', value: 'all' },
      { label: '未读', value: 'unread' }
    ],

    // 用户信息
    userInfo: null,

    // 弹窗相关
    showDetailPopup: false,        // 详情弹窗显示状态
    selectedNotification: null     // 当前选中的通知
  },

  /**
   * 生命周期函数--监听页面加载
   *
   * 页面加载时执行的初始化操作：
   * 1. 获取用户信息
   * 2. 加载通知列表
   */
  onLoad(options) {
    console.log('通知页面加载');
    this.initPage();
  },



  /**
   * 生命周期函数--监听页面显示
   * 
   * 每次页面显示时都会执行，用于：
   * 1. 刷新通知列表（可能有新通知）
   * 2. 更新未读状态
   */
  onShow() {
    console.log('通知页面显示');
    // 如果已经初始化过，则刷新数据
    if (this.data.userInfo) {
      this.refreshNotifications();
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   * 
   * 下拉刷新功能：
   * 1. 重置分页参数
   * 2. 重新加载第一页数据
   * 3. 停止下拉刷新动画
   */
  onPullDownRefresh() {
    console.log('下拉刷新通知列表');
    this.refreshNotifications();
  },

  /**
   * 页面上拉触底事件的处理函数
   * 
   * 上拉加载更多功能：
   * 1. 检查是否还有更多数据
   * 2. 加载下一页数据
   * 3. 追加到现有列表
   */
  onReachBottom() {
    console.log('上拉加载更多通知');
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreNotifications();
    }
  },

  /**
   * 初始化页面
   * 
   * 执行步骤：
   * 1. 获取用户登录状态和信息
   * 2. 加载通知列表
   */
  async initPage() {
    try {
      // 获取用户信息
      const userInfo = wx.getStorageSync('userInfo');
      console.log('获取到的用户信息:', userInfo);

      if (!userInfo || !userInfo.openid) {
        console.log('用户未登录或openid不存在');
        showError(this, '请先登录');
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }

      console.log('用户已登录，openid:', userInfo.openid);
      this.setData({ userInfo });

      // 加载通知列表
      await this.loadNotifications();
    } catch (error) {
      console.error('初始化通知页面失败:', error);
      showError(this, '页面初始化失败');
    }
  },

  /**
   * 选项卡切换事件处理
   * 
   * 当用户切换"全部"和"未读"选项卡时：
   * 1. 更新当前筛选条件
   * 2. 重置分页参数
   * 3. 重新加载数据
   * 
   * @param {Object} event 事件对象
   * @param {string} event.detail.value 选中的选项卡值
   */
  onTabChange(event) {
    const filter = event.detail.value;
    console.log('切换通知筛选:', filter);
    
    this.setData({
      currentFilter: filter,
      page: 1,
      hasMore: true,
      notifications: []
    });
    
    this.loadNotifications();
  },

  /**
   * 加载通知列表
   * 
   * 核心数据加载函数：
   * 1. 调用云函数获取通知数据
   * 2. 处理分页逻辑
   * 3. 更新页面状态
   * 
   * @param {boolean} append 是否追加到现有列表（用于分页加载）
   */
  async loadNotifications(append = false) {
    if (this.data.loading) return;

    console.log('开始加载通知，参数:', {
      userId: this.data.userInfo?.openid,
      page: this.data.page,
      pageSize: this.data.pageSize,
      filter: this.data.currentFilter,
      append: append
    });

    this.setData({ loading: true });

    try {
      const callData = {
        name: 'notificationManagement',
        data: {
          action: 'getNotifications',
          data: {
            userId: this.data.userInfo.openid,
            page: this.data.page,
            pageSize: this.data.pageSize,
            filter: this.data.currentFilter
          }
        }
      };

      console.log('调用云函数，参数:', callData);

      const result = await wx.cloud.callFunction(callData);

      console.log('云函数返回结果:', result);

      if (result.result && result.result.success) {
        const { notifications, hasMore, total } = result.result.data;

        console.log('解析通知数据:', {
          notificationCount: notifications.length,
          total: total,
          hasMore: hasMore,
          firstNotification: notifications[0]
        });

        // 格式化通知数据
        const formattedNotifications = notifications.map(notification => ({
          ...notification,
          formattedTime: this.formatTime(notification.createTime),
          isToday: this.isToday(notification.createTime)
        }));

        const finalNotifications = append ?
          [...this.data.notifications, ...formattedNotifications] :
          formattedNotifications;

        console.log('设置页面数据:', {
          finalCount: finalNotifications.length,
          hasMore: hasMore
        });

        this.setData({
          notifications: finalNotifications,
          hasMore,
          loading: false
        });

        console.log('页面数据设置完成，当前状态:', {
          notificationsLength: this.data.notifications.length,
          loading: this.data.loading,
          hasMore: this.data.hasMore
        });
      } else {
        const errorMsg = result.result ? result.result.message : '云函数调用失败';
        console.error('云函数返回错误:', errorMsg, result);
        throw new Error(errorMsg);
      }
    } catch (error) {
      console.error('加载通知列表失败:', error);
      console.error('错误堆栈:', error.stack);

      showError(this, '加载失败');
      this.setData({ loading: false });
    }
    
    // 停止下拉刷新
    wx.stopPullDownRefresh();
  },

  /**
   * 刷新通知列表
   * 
   * 重置分页参数并重新加载第一页数据
   */
  refreshNotifications() {
    this.setData({
      page: 1,
      hasMore: true,
      notifications: []
    });
    this.loadNotifications();
  },

  /**
   * 加载更多通知
   * 
   * 分页加载下一页数据
   */
  loadMoreNotifications() {
    this.setData({
      page: this.data.page + 1
    });
    this.loadNotifications(true);
  },

  /**
   * 通知项点击事件处理
   *
   * 用户点击通知项时：
   * 1. 显示通知详情弹窗
   * 2. 标记为已读（如果未读）
   *
   * @param {Object} event 事件对象
   */
  async onNotificationTap(event) {
    const { notification } = event.currentTarget.dataset;
    console.log('点击通知:', notification);

    // 设置选中的通知并显示详情弹窗
    this.setData({
      selectedNotification: notification,
      showDetailPopup: true
    });

    // 如果是未读通知，标记为已读
    if (!notification.isRead) {
      await this.markAsRead(notification._id);
    }
  },

  /**
   * 标记通知为已读
   * 
   * @param {string} notificationId 通知ID
   */
  async markAsRead(notificationId) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'notificationManagement',
        data: {
          action: 'markAsRead',
          data: {
            notificationId,
            userId: this.data.userInfo.openid
          }
        }
      });

      if (result.result.success) {
        // 更新本地数据
        const notifications = this.data.notifications.map(item => {
          if (item._id === notificationId) {
            return { ...item, isRead: true };
          }
          return item;
        });
        this.setData({ notifications });
      }
    } catch (error) {
      console.error('标记已读失败:', error);
    }
  },

  /**
   * 全部标记为已读
   */
  async markAllAsRead() {
    try {
      showLoading(this, '处理中...');

      const result = await wx.cloud.callFunction({
        name: 'notificationManagement',
        data: {
          action: 'markAllAsRead',
          data: {
            userId: this.data.userInfo.openid
          }
        }
      });

      hideToast(this);

      if (result.result.success) {
        showSuccess(this, '全部已读');

        // 刷新列表
        this.refreshNotifications();
      } else {
        throw new Error(result.result.message);
      }
    } catch (error) {
      hideToast(this);
      console.error('全部标记已读失败:', error);
      showError(this, '操作失败');
    }
  },

  /**
   * 清空所有消息通知
   */
  async clearAllNotifications() {
    try {
      // 显示确认对话框
      const result = await wx.showModal({
        title: '确认清空',
        content: '确定要清空所有消息通知吗？此操作不可恢复。',
        confirmText: '清空',
        confirmColor: '#ff4757',
        cancelText: '取消'
      });

      if (!result.confirm) {
        return; // 用户取消操作
      }

      showLoading(this, '清空中...');

      // 调用云函数清空所有通知
      const clearResult = await wx.cloud.callFunction({
        name: 'notificationManagement',
        data: {
          action: 'clearAllNotifications',
          data: {
            userId: this.data.userInfo.openid
          }
        }
      });

      hideToast(this);

      if (clearResult.result.success) {
        showSuccess(this, '清空成功');

        // 清空本地列表
        this.setData({
          notifications: [],
          hasMore: false
        });
      } else {
        throw new Error(clearResult.result.message);
      }
    } catch (error) {
      hideToast(this);
      console.error('清空消息失败:', error);
      showError(this, '清空失败');
    }
  },

  /**
   * 跳转到相关页面
   * 
   * 根据通知类型和关联数据跳转到对应页面
   * 
   * @param {Object} notification 通知对象
   */
  navigateToRelatedPage(notification) {
    const { type, courseId, bookingId } = notification;

    // 根据通知类型决定跳转页面
    if (courseId) {
      // 有课程ID的通知，跳转到课程详情
      wx.navigateTo({
        url: `/pages/course-detail/course-detail?id=${courseId}`
      });
    } else {
      // 其他类型的通知，可以根据需要添加更多跳转逻辑
      console.log('暂无相关页面跳转');
    }
  },

  /**
   * 格式化时间显示
   *
   * 将时间戳转换为用户友好的时间格式
   * 确保使用24小时制和正确的时区
   *
   * @param {Date|string|number} time 时间
   * @returns {string} 格式化后的时间字符串
   */
  formatTime(time) {
    // 确保转换为有效的Date对象
    let date;



    // 处理不同的时间格式
    if (typeof time === 'string') {
      // 如果是字符串，可能是ISO格式或其他格式
      date = new Date(time);
    } else if (typeof time === 'number') {
      // 如果是数字，可能是时间戳
      date = new Date(time);
    } else if (time instanceof Date) {
      // 如果已经是Date对象
      date = new Date(time.getTime());
    } else {
      console.error('无效的时间格式:', time);
      return '时间格式错误';
    }

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.error('无效的时间格式:', time);
      return '时间格式错误';
    }

    // 获取当前时间
    const now = new Date();
    const diff = now.getTime() - date.getTime();





    // 1分钟内
    if (diff < 60 * 1000 && diff >= 0) {
      return '刚刚';
    }

    // 1小时内
    if (diff < 60 * 60 * 1000 && diff >= 0) {
      const minutes = Math.floor(diff / (60 * 1000));
      return `${minutes}分钟前`;
    }

    // 24小时内（今天）
    if (this.isToday(date)) {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `今天 ${hours}:${minutes}`;
    }

    // 昨天
    if (this.isYesterday(date)) {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `昨天 ${hours}:${minutes}`;
    }

    // 其他日期（显示完整日期和24小时制时间）
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');

    // 如果是今年，不显示年份
    const currentYear = now.getFullYear();
    const dateYear = date.getFullYear();

    if (dateYear === currentYear) {
      return `${month}月${day}日 ${hours}:${minutes}`;
    } else {
      return `${dateYear}年${month}月${day}日 ${hours}:${minutes}`;
    }
  },

  /**
   * 判断是否为今天
   * 使用本地时区进行日期比较
   *
   * @param {Date|string|number} time 要判断的时间
   * @returns {boolean} 是否为今天
   */
  isToday(time) {
    // 确保转换为有效的Date对象
    const date = new Date(time);

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.error('无效的时间格式:', time);
      return false;
    }

    const today = new Date();

    // 使用年月日进行比较，忽略时分秒
    const dateStr = `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`;
    const todayStr = `${today.getFullYear()}-${today.getMonth()}-${today.getDate()}`;



    return dateStr === todayStr;
  },

  /**
   * 判断是否为昨天
   * 使用本地时区进行日期比较
   *
   * @param {Date|string|number} time 要判断的时间
   * @returns {boolean} 是否为昨天
   */
  isYesterday(time) {
    // 确保转换为有效的Date对象
    const date = new Date(time);

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.error('无效的时间格式:', time);
      return false;
    }

    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);

    // 使用年月日进行比较，忽略时分秒
    const dateStr = `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`;
    const yesterdayStr = `${yesterday.getFullYear()}-${yesterday.getMonth()}-${yesterday.getDate()}`;



    return dateStr === yesterdayStr;
  },

  /**
   * SwipeCell滑动操作事件处理
   *
   * 用户点击SwipeCell右侧操作按钮时的处理函数
   *
   * @param {Object} event 事件对象
   */
  async onSwipeAction(event) {
    // 这个方法现在主要用于处理SwipeCell的其他操作
    // 删除操作由onDeleteAction方法处理
    console.log('SwipeCell操作:', event.detail);
  },

  /**
   * 删除操作按钮点击事件
   *
   * 用户点击自定义删除按钮时的处理函数
   *
   * @param {Object} event 事件对象
   */
  async onDeleteAction(event) {
    const { notification } = event.currentTarget.dataset;

    if (!notification) {
      showError(this, '操作失败：数据错误');
      return;
    }

    // 执行删除操作
    await this.deleteNotification(notification);
  },

  /**
   * 删除通知
   *
   * 统一的删除通知处理函数
   *
   * @param {Object} notification 通知对象
   */
  async deleteNotification(notification) {
    try {
      // 显示确认对话框
      const result = await wx.showModal({
        title: '确认删除',
        content: '确定要删除这条通知吗？',
        confirmText: '删除',
        confirmColor: '#ff4757'
      });

      if (!result.confirm) {
        return; // 用户取消删除
      }

      showLoading(this, '删除中...');

      // 调用云函数删除通知
      const deleteResult = await wx.cloud.callFunction({
        name: 'notificationManagement',
        data: {
          action: 'deleteNotification',
          data: {
            notificationId: notification._id,
            userId: this.data.userInfo.openid
          }
        }
      });

      hideToast(this);

      if (deleteResult.result.success) {
        showSuccess(this, '删除成功');

        // 从本地列表中移除该通知
        const notifications = this.data.notifications.filter(
          item => item._id !== notification._id
        );
        this.setData({ notifications });

      } else {
        throw new Error(deleteResult.result.message);
      }
    } catch (error) {
      hideToast(this);
      console.error('删除通知失败:', error);
      showError(this, '删除失败');
    }
  },

  /**
   * 详情弹窗状态改变事件处理
   *
   * @param {Object} event 事件对象
   */
  onDetailPopupChange(event) {
    const { visible } = event.detail;
    this.setData({
      showDetailPopup: visible
    });

    // 如果弹窗关闭，清空选中的通知
    if (!visible) {
      this.setData({
        selectedNotification: null
      });
    }
  },

  /**
   * 关闭详情弹窗
   */
  closeDetailPopup() {
    this.setData({
      showDetailPopup: false,
      selectedNotification: null
    });
  },



  /**
   * 删除选中的通知
   */
  async deleteSelectedNotification() {
    const notification = this.data.selectedNotification;
    if (!notification) {
      return;
    }

    // 关闭弹窗
    this.closeDetailPopup();

    // 使用统一的删除方法
    await this.deleteNotification(notification);
  },


});
